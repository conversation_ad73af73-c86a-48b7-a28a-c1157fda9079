// Smokey AI - Enhanced JavaScript
class SmokeyAI {
    constructor() {
        this.currentUser = this.getCurrentUser();
        this.currentChatId = null;
        this.isLoading = false;

        // Check if user is logged in
        if (!this.currentUser) {
            // Create demo user for testing
            this.createDemoUser();
        }

        this.chatHistory = this.loadChatHistory();
        this.userMemory = this.loadUserMemory();

        // DOM Elements
        this.elements = {
            input: document.getElementById('input'),
            output: document.getElementById('output'),
            sendButton: document.getElementById('send-button'),
            newChatBtn: document.getElementById('new-chat'),
            clearHistoryBtn: document.getElementById('clear-history'),
            chatHistory: document.getElementById('chat-history'),
            welcomeMessage: document.getElementById('welcome-message'),
            mobileMenuToggle: document.getElementById('mobile-menu-toggle'),
            sidebar: document.getElementById('sidebar'),
            characterCount: document.getElementById('character-count'),
            typingIndicator: document.getElementById('typing-indicator'),
            totalChats: document.getElementById('total-chats'),
            totalMessages: document.getElementById('total-messages'),
            quickActions: document.getElementById('quick-actions'),
            attachButton: document.getElementById('attach-button'),
            imageGeneratorBtn: document.getElementById('image-generator-btn'),
            imageModal: document.getElementById('image-modal'),
            closeImageModal: document.getElementById('close-image-modal'),
            cancelImageGeneration: document.getElementById('cancel-image-generation'),
            generateImageBtn: document.getElementById('generate-image-btn'),
            imagePrompt: document.getElementById('image-prompt'),
            promptCounter: document.getElementById('prompt-counter'),
            imageStyle: document.getElementById('image-style'),
            imageSize: document.getElementById('image-size'),
            imageQuality: document.getElementById('image-quality'),
            userIndicator: document.getElementById('user-indicator'),
            userName: document.getElementById('user-name'),
            profileBtn: document.getElementById('profile-btn'),
            logoutBtn: document.getElementById('logout-btn'),
            toolsBtn: document.getElementById('tools-btn'),
            toolsPanel: document.getElementById('tools-panel'),
            calculatorModal: document.getElementById('calculator-modal'),
            memoryIndicator: document.getElementById('memory-indicator'),
            memoryCount: document.getElementById('memory-count'),
            loadingOverlay: document.getElementById('loading-overlay')
        };

        this.init();
    }

    init() {
        console.log('🚀 Initializing Smokey AI...');

        // FIXED: Better initialization with error handling
        try {
            this.setupEventListeners();
            this.updateUserDisplay();
            this.updateChatHistoryUI();
            this.updateStats();
            this.updateMemoryIndicator();
            this.startNewChat();
            this.setupTextareaAutoResize();

            // Ensure input focus for better UX
            setTimeout(() => {
                if (this.elements.input) {
                    this.elements.input.focus();
                }
            }, 500);

            console.log('✅ Smokey AI initialized successfully');
        } catch (error) {
            console.error('❌ Error during initialization:', error);
            this.showToast('Error initializing Smokey AI. Please refresh the page.', 'error');
        }
    }

    setupEventListeners() {
        // Send message events - FIXED: Ensure both methods work
        if (this.elements.sendButton) {
            this.elements.sendButton.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('🔘 Send button clicked');
                this.sendMessage();
            });
        }

        if (this.elements.input) {
            this.elements.input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    console.log('⌨️ Enter key pressed');
                    this.sendMessage();
                }
            });

            // Character count and auto-resize
            this.elements.input.addEventListener('input', () => {
                this.updateCharacterCount();
                this.autoResizeTextarea();
            });
        }

        // New chat - FIXED: Add null check
        if (this.elements.newChatBtn) {
            this.elements.newChatBtn.addEventListener('click', () => this.startNewChat());
        }

        // Clear history - FIXED: Add null check
        if (this.elements.clearHistoryBtn) {
            this.elements.clearHistoryBtn.addEventListener('click', () => this.clearAllHistory());
        }

        // Quick actions - FIXED: Add null check
        if (this.elements.quickActions) {
            this.elements.quickActions.addEventListener('click', (e) => {
                const btn = e.target.closest('.quick-action-btn');
                if (btn) {
                    this.handleQuickAction(btn.dataset.action);
                }
            });
        }

        // Attach button (placeholder) - FIXED: Add null check
        if (this.elements.attachButton) {
            this.elements.attachButton.addEventListener('click', () => {
                this.showToast('File attachment feature coming soon! 📎');
            });
        }

        // Image generator button - FIXED: Ensure proper click handling
        if (this.elements.imageGeneratorBtn) {
            this.elements.imageGeneratorBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('🎨 Image generator button clicked');
                this.openImageModal();
            });
        }

        // Update image generator button status
        this.updateImageGeneratorStatus();

        // Update Groq API status
        this.updateGroqStatus();

        // Update DeepSeek API status
        this.updateDeepSeekStatus();

        // Ensure user display is updated
        this.updateUserDisplay();

        // Show welcome message on first load
        this.showWelcomeMessage();

        // Debug: Check if calculator modal exists
        setTimeout(() => {
            const calcModal = document.getElementById('calculator-modal');
            console.log('🧮 Calculator modal check:', calcModal ? 'Found' : 'Not found');
            if (calcModal) {
                console.log('🧮 Calculator modal classes:', calcModal.className);
            }
        }, 1000);

        // Image modal events - FIXED: Better error handling
        if (this.elements.closeImageModal) {
            this.elements.closeImageModal.addEventListener('click', () => {
                this.closeImageModal();
            });
        }

        if (this.elements.cancelImageGeneration) {
            this.elements.cancelImageGeneration.addEventListener('click', () => {
                this.closeImageModal();
            });
        }

        if (this.elements.generateImageBtn) {
            this.elements.generateImageBtn.addEventListener('click', () => {
                this.generateImage();
            });
        }

        // Image prompt counter - FIXED: Better error handling
        if (this.elements.imagePrompt) {
            this.elements.imagePrompt.addEventListener('input', () => {
                this.updatePromptCounter();
            });
        }

        // Example prompts
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('example-prompt')) {
                const prompt = e.target.dataset.prompt;
                this.elements.imagePrompt.value = prompt;
                this.updatePromptCounter();
            }
        });

        // Close modal on backdrop click - FIXED: Better error handling
        if (this.elements.imageModal) {
            this.elements.imageModal.addEventListener('click', (e) => {
                if (e.target === this.elements.imageModal) {
                    this.closeImageModal();
                }
            });
        }

        // Memory indicator - FIXED: Add null check
        if (this.elements.memoryIndicator) {
            this.elements.memoryIndicator.addEventListener('click', () => {
                this.displayMessage('/memory', 'user');
                this.displayMessage(this.getMemorySummary(), 'ai');
            });
        }

        // Profile button - FIXED: Ensure proper click handling
        if (this.elements.profileBtn) {
            this.elements.profileBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('👤 Profile button clicked');
                this.openProfile();
            });
        }

        // Tools menu - FIXED: Ensure proper setup
        this.setupToolsMenu();

        // Logout button - FIXED: Add null check
        if (this.elements.logoutBtn) {
            this.elements.logoutBtn.addEventListener('click', () => {
                if (confirm('Are you sure you want to logout?')) {
                    this.logout();
                }
            });
        }

        // Mobile menu - FIXED: Add null check
        if (this.elements.mobileMenuToggle) {
            this.elements.mobileMenuToggle.addEventListener('click', () => this.toggleMobileMenu());
        }

        // Close mobile menu when clicking outside - FIXED: Add null checks
        document.addEventListener('click', (e) => {
            if (this.elements.sidebar && this.elements.mobileMenuToggle &&
                !this.elements.sidebar.contains(e.target) &&
                !this.elements.mobileMenuToggle.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'n':
                        e.preventDefault();
                        this.startNewChat();
                        break;
                    case 'k':
                        e.preventDefault();
                        if (this.elements.input) {
                            this.elements.input.focus();
                        }
                        break;
                    case 'l':
                        e.preventDefault();
                        this.clearAllHistory();
                        break;
                }
            }

            // Escape to close mobile menu
            if (e.key === 'Escape') {
                this.closeMobileMenu();
            }
        });
    }

    async sendMessage() {
        // FIXED: Better validation and error handling
        if (!this.elements.input) {
            console.error('❌ Input element not found');
            this.showToast('Input error. Please refresh the page.', 'error');
            return;
        }

        const message = this.elements.input.value.trim();
        console.log('📤 Sending message:', message);

        if (!message || this.isLoading) {
            console.log('⚠️ Message empty or already loading');
            return;
        }

        // Check for welcome commands first
        if (this.handleWelcomeCommands(message)) {
            this.elements.input.value = '';
            this.autoResizeTextarea(); // FIXED: Use existing function
            return;
        }

        // Check for memory commands
        if (this.handleMemoryCommands(message)) {
            this.elements.input.value = '';
            this.updateCharacterCount();
            return;
        }

        // Hide welcome message
        this.hideWelcomeMessage();

        // Display user message
        this.displayMessage(message, 'user');

        // Clear input
        this.elements.input.value = '';
        this.updateCharacterCount();

        // Add to chat history
        this.addMessageToCurrentChat(message, 'user');

        // Extract and save memory from user message
        this.extractAndSaveMemory(message);

        try {
            // Check for memory-related questions
            const memoryResponse = this.checkMemoryQuestions(message.toLowerCase());
            let response;

            if (memoryResponse) {
                // Show loading for memory responses
                this.showLoading('Accessing memory');
                await this.delay(500);
                response = memoryResponse;
            } else {
                // Check for custom replies
                const customReply = this.getCustomReply(message.toLowerCase());

                if (customReply) {
                    // Show loading for custom replies
                    this.showLoading('Preparing response');
                    await this.delay(500);
                    response = customReply;
                } else {
                    // Determine task type and route to appropriate API
                    const taskType = this.determineTaskType(message);
                    console.log(`🎯 Task type detected: ${taskType}`);

                    // Enhance message with memory context
                    const enhancedMessage = this.enhanceMessageWithMemory(message);

                    // Smart routing based on task type
                    if (this.isCodingTask(taskType)) {
                        // Route coding tasks to DeepSeek
                        this.showLoading(`Processing with DeepSeek ${this.selectDeepSeekModel(taskType)}`);
                        response = await this.getDeepSeekResponse(enhancedMessage, taskType);
                    } else {
                        // Route other tasks to Groq
                        const model = this.getModelForTask(taskType);
                        this.showLoading(`Processing with ${model}`);
                        response = await this.getGroqResponse(enhancedMessage, model);
                    }
                }
            }

            // Hide loading
            this.hideLoading();

            // Display AI response
            this.displayMessage(response, 'ai');

            // Add to chat history
            this.addMessageToCurrentChat(response, 'ai');

            // Update chat title if this is the first message
            this.updateChatTitle(message);

        } catch (error) {
            console.error('Error:', error);
            this.hideLoading();
            this.displayMessage('Sorry, I encountered an error. Please try again.', 'error');
        }
    }

    getCustomReply(message) {
        const customReplies = {
            "who are you": `Hi there! I'm **Smokey AI**, your smart assistant 🐾

I'm designed to help you with:
- Answering questions
- Coding assistance
- Creative brainstorming
- General conversations

What would you like to explore today?`,

            "what is your name": "I'm **Smokey AI**, at your service! 😊",

            "who made you": `I was created by **Pranav** and his amazing team! 🚀

Here's what makes me special:
1. Built with modern web technologies
2. Powered by Google's Gemini API
3. Designed for natural conversations
4. Named after Pranav's cat! 🐱`,

            "hello": `Hello! I'm **Smokey AI**. How can I help you today? 😊

Try asking me about:
- **Code help** - I can assist with programming
- **Explanations** - I'll break down complex topics
- **Creative ideas** - Let's brainstorm together
- **General questions** - Ask me anything!`,

            "hi": "Hi there! What can I do for you? 👋",

            "how are you": `I'm doing **great**! Thanks for asking. 😊

I'm ready to help you with whatever you need. How are *you* doing today?`,

            "what can you do": `Great question! Here's what I can help you with:

**💻 Coding & Development**
- Write and debug code
- Explain programming concepts
- Review your code

**📚 Learning & Education**
- Explain complex topics simply
- Break down step-by-step processes
- Answer questions on various subjects

**🎨 Creative Tasks**
- Brainstorm ideas
- Help with writing
- Creative problem solving

**💬 General Assistance**
- Have natural conversations
- Provide information
- Help with daily tasks

What would you like to explore?`,

            "help": `I'm here to help! Here are some ways to get started:

**Quick Actions** (try the buttons above):
- 💡 **Explain** - Get simple explanations
- 📝 **Summarize** - Condense information
- 💻 **Code Help** - Programming assistance
- 🎨 **Creative** - Brainstorming ideas

**Example questions you can ask:**
\`\`\`
How do I create a website?
Explain machine learning simply
Help me debug this JavaScript code
What are some creative project ideas?
\`\`\`

**Keyboard shortcuts:**
- \`Ctrl+N\` - New chat
- \`Ctrl+K\` - Focus input
- \`Enter\` - Send message
- \`Shift+Enter\` - New line

What can I help you with today?`,

            "test formatting": `Sure! Here's a demonstration of my **ChatGPT-style formatting**:

## Text Formatting
I can format text with **bold** and *italic* styles.

## Lists
Here's a numbered list:
1. First item with important info
2. Second item with more details
3. Third item to wrap things up

And here's a bullet list:
- Simple bullet point
- Another important point
- Final bullet item

## Code Examples
Inline code looks like this: \`console.log('Hello World')\`

Here's a JavaScript code block:
\`\`\`javascript
function greetUser(name) {
    console.log(\`Hello, \${name}!\`);
    return \`Welcome to Smokey AI!\`;
}

greetUser('Developer');
\`\`\`

## Paragraphs
This is a paragraph with proper spacing. It's designed to be easy to read and well-formatted.

This is another paragraph that demonstrates how text flows naturally with proper line breaks and spacing.

Pretty cool, right? 🚀`
        };

        return customReplies[message] || null;
    }

    async fetchGeminiAPI(message) {
        // Replace with your Gemini API endpoint and API key
        const API_KEY = 'AIzaSyCy3blF5PR3SGphlvi1sqRHzo69ZaX-OcE';
        const API_ENDPOINT = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`;

        const response = await fetch(API_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: message
                    }]
                }]
            })
        });

        if (!response.ok) {
            throw new Error(`API Error: ${response.status}`);
        }

        const data = await response.json();
        return data.candidates[0].content.parts[0].text;
    }

    displayMessage(message, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', sender);

        if (sender === 'ai') {
            // Format AI messages with ChatGPT-style formatting
            messageDiv.innerHTML = this.formatAIResponse(message);
        } else {
            // Keep user messages as plain text
            messageDiv.textContent = message;
        }

        this.elements.output.appendChild(messageDiv);

        // FIXED: Ensure scroll happens after DOM update
        requestAnimationFrame(() => {
            this.scrollToBottom();
        });

        // Add typing animation for AI messages
        if (sender === 'ai') {
            this.animateMessageAppearance(messageDiv);
            // Scroll again after animation
            setTimeout(() => {
                this.scrollToBottom();
            }, 100);
        }
    }

    formatAIResponse(message) {
        // First, escape HTML to prevent XSS
        let formatted = this.escapeHtml(message);

        // Format code blocks (```language or ```)
        formatted = this.formatCodeBlocks(formatted);

        // Format inline code (`code`)
        formatted = this.formatInlineCode(formatted);

        // Format numbered lists (1. 2. 3.)
        formatted = this.formatNumberedLists(formatted);

        // Format bullet points (- or *)
        formatted = this.formatBulletPoints(formatted);

        // Format paragraphs (double line breaks)
        formatted = this.formatParagraphs(formatted);

        // Format bold text (**text**)
        formatted = this.formatBoldText(formatted);

        // Format italic text (*text*)
        formatted = this.formatItalicText(formatted);

        return formatted;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatCodeBlocks(text) {
        // Handle multi-line code blocks with language specification
        text = text.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, language, code) => {
            const lang = language || 'text';
            const trimmedCode = code.trim();
            return `<div class="code-block">
                <div class="code-header">
                    <span class="code-language">${lang}</span>
                    <button class="copy-code-btn" onclick="copyCode(this)" title="Copy code">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                        </svg>
                    </button>
                </div>
                <pre><code class="language-${lang}">${trimmedCode}</code></pre>
            </div>`;
        });

        return text;
    }

    formatInlineCode(text) {
        // Handle inline code `code`
        return text.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');
    }

    formatNumberedLists(text) {
        // Handle numbered lists (1. 2. 3.)
        const lines = text.split('\n');
        let inList = false;
        let result = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const numberedMatch = line.match(/^(\d+)\.\s+(.+)$/);

            if (numberedMatch) {
                if (!inList) {
                    result.push('<ol class="formatted-list">');
                    inList = true;
                }
                result.push(`<li>${numberedMatch[2]}</li>`);
            } else {
                if (inList) {
                    result.push('</ol>');
                    inList = false;
                }
                result.push(line);
            }
        }

        if (inList) {
            result.push('</ol>');
        }

        return result.join('\n');
    }

    formatBulletPoints(text) {
        // Handle bullet points (- or *)
        const lines = text.split('\n');
        let inList = false;
        let result = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const bulletMatch = line.match(/^[-*]\s+(.+)$/);

            if (bulletMatch) {
                if (!inList) {
                    result.push('<ul class="formatted-list">');
                    inList = true;
                }
                result.push(`<li>${bulletMatch[1]}</li>`);
            } else {
                if (inList) {
                    result.push('</ul>');
                    inList = false;
                }
                result.push(line);
            }
        }

        if (inList) {
            result.push('</ul>');
        }

        return result.join('\n');
    }

    formatParagraphs(text) {
        // Split by double line breaks and wrap in paragraphs
        const paragraphs = text.split('\n\n').filter(p => p.trim());
        return paragraphs.map(p => {
            const trimmed = p.trim();
            if (trimmed.startsWith('<') || trimmed.includes('<ol') || trimmed.includes('<ul')) {
                return trimmed; // Don't wrap HTML elements
            }
            return `<p class="formatted-paragraph">${trimmed.replace(/\n/g, '<br>')}</p>`;
        }).join('');
    }

    formatBoldText(text) {
        // Handle **bold** text
        return text.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
    }

    formatItalicText(text) {
        // Handle *italic* text (but not ** which is bold)
        // FIXED: Remove lookbehind assertion for better browser compatibility
        return text.replace(/\*([^*]+)\*/g, (match, content) => {
            // Don't replace if it's part of **bold** text
            if (match.includes('**')) {
                return match;
            }
            return `<em>${content}</em>`;
        });
    }

    animateMessageAppearance(messageDiv) {
        messageDiv.style.opacity = '0';
        messageDiv.style.transform = 'translateY(20px)';

        setTimeout(() => {
            messageDiv.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            messageDiv.style.opacity = '1';
            messageDiv.style.transform = 'translateY(0)';
        }, 100);
    }

    showLoading(message = null) {
        this.isLoading = true;
        this.elements.sendButton.disabled = true;
        this.elements.typingIndicator.classList.add('show');

        const config = getConfig();
        let loadingText = 'Smokey is thinking';

        if (config.groq.enabled) {
            loadingText = message || 'Groq AI is processing';
        }

        const loadingDiv = document.createElement('div');
        loadingDiv.classList.add('loading-message');
        loadingDiv.innerHTML = `
            <span>${loadingText}</span>
            <div class="loading-dots">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
            </div>
        `;

        this.elements.output.appendChild(loadingDiv);
        this.scrollToBottom();
    }

    hideLoading() {
        this.isLoading = false;
        this.elements.sendButton.disabled = false;
        this.elements.typingIndicator.classList.remove('show');

        const loadingMessage = this.elements.output.querySelector('.loading-message');
        if (loadingMessage) {
            loadingMessage.remove();
        }
    }

    hideWelcomeMessage() {
        if (this.elements.welcomeMessage) {
            this.elements.welcomeMessage.style.display = 'none';
        }
    }

    scrollToBottom() {
        // FIXED: Improved scroll behavior with proper timing and smooth scrolling
        if (this.elements.output) {
            // Use requestAnimationFrame for smooth scrolling
            requestAnimationFrame(() => {
                this.elements.output.scrollTop = this.elements.output.scrollHeight;

                // Also scroll the chat container if it exists
                const chatContainer = document.querySelector('.chat-container');
                if (chatContainer) {
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }

                console.log('📜 Scrolled to bottom');
            });
        }
    }

    updateCharacterCount() {
        const count = this.elements.input.value.length;
        this.elements.characterCount.textContent = `${count}/1000`;

        if (count > 900) {
            this.elements.characterCount.style.color = '#ef4444';
        } else if (count > 800) {
            this.elements.characterCount.style.color = '#f59e0b';
        } else {
            this.elements.characterCount.style.color = 'var(--text-secondary)';
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Enhanced UI Methods
    setupTextareaAutoResize() {
        // FIXED: Add null check for better error handling
        if (this.elements.input) {
            this.elements.input.style.height = 'auto';
            this.elements.input.style.height = this.elements.input.scrollHeight + 'px';
        }
    }

    autoResizeTextarea() {
        // FIXED: Add null check for better error handling
        if (this.elements.input) {
            this.elements.input.style.height = 'auto';
            this.elements.input.style.height = Math.min(this.elements.input.scrollHeight, 120) + 'px';
        }
    }

    handleQuickAction(action) {
        const prompts = {
            explain: "Please explain this concept in simple terms: ",
            summarize: "Please summarize the following: ",
            code: "I need help with coding. Can you assist me with: ",
            creative: "Help me brainstorm creative ideas for: "
        };

        const prompt = prompts[action];
        if (prompt) {
            this.elements.input.value = prompt;
            this.elements.input.focus();
            this.elements.input.setSelectionRange(prompt.length, prompt.length);
            this.updateCharacterCount();
            this.autoResizeTextarea();
        }
    }

    clearAllHistory() {
        if (confirm('Are you sure you want to clear all chat history? This cannot be undone.')) {
            this.chatHistory = [];
            this.saveChatHistory();
            this.updateChatHistoryUI();
            this.updateStats();
            this.startNewChat();
            this.showToast('Chat history cleared! 🗑️');
        }
    }

    updateStats() {
        const totalChats = this.chatHistory.length;
        const totalMessages = this.chatHistory.reduce((total, chat) => total + chat.messages.length, 0);

        this.elements.totalChats.textContent = totalChats;
        this.elements.totalMessages.textContent = totalMessages;
    }

    showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--${type === 'error' ? 'danger' : 'success'});
            color: white;
            padding: 1rem 1.5rem;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            font-weight: 500;
            max-width: 300px;
            font-size: 0.9rem;
            line-height: 1.4;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // Chat Management Methods
    startNewChat() {
        this.currentChatId = Date.now();
        this.elements.output.innerHTML = '';
        this.elements.welcomeMessage.style.display = 'flex';
        this.updateChatHistoryUI();
        this.updateStats();
        this.elements.input.focus();
    }

    loadChatHistory() {
        try {
            if (!this.currentUser) return [];

            const userData = this.loadCurrentUserData();
            return userData ? userData.savedChats || [] : [];
        } catch (error) {
            console.error('Error loading chat history:', error);
            return [];
        }
    }

    saveChatHistory() {
        try {
            if (!this.currentUser) return;

            const userData = this.loadCurrentUserData() || {
                username: this.currentUser.username,
                memory: {},
                savedChats: [],
                settings: { theme: 'dark', notifications: true },
                createdAt: new Date().toISOString(),
                lastLogin: new Date().toISOString()
            };

            userData.savedChats = this.chatHistory;
            this.saveCurrentUserData(userData);
        } catch (error) {
            console.error('Error saving chat history:', error);
        }
    }

    addMessageToCurrentChat(message, sender) {
        let currentChat = this.chatHistory.find(chat => chat.id === this.currentChatId);

        if (!currentChat) {
            currentChat = {
                id: this.currentChatId,
                title: "New Chat",
                messages: [],
                timestamp: Date.now()
            };
            this.chatHistory.unshift(currentChat);
        }

        currentChat.messages.push({
            text: message,
            sender: sender,
            timestamp: Date.now()
        });

        this.saveChatHistory();
        this.updateChatHistoryUI();
        this.updateStats();
    }

    updateChatTitle(firstMessage) {
        const currentChat = this.chatHistory.find(chat => chat.id === this.currentChatId);
        if (currentChat && currentChat.title === "New Chat") {
            currentChat.title = firstMessage.length > 30 ?
                firstMessage.substring(0, 30) + "..." :
                firstMessage;
            this.saveChatHistory();
            this.updateChatHistoryUI();
        }
    }

    loadChat(chatId) {
        const chat = this.chatHistory.find(c => c.id === chatId);
        if (!chat) return;

        this.currentChatId = chatId;
        this.elements.output.innerHTML = '';
        this.elements.welcomeMessage.style.display = 'none';

        chat.messages.forEach(msg => {
            this.displayMessage(msg.text, msg.sender);
        });

        this.updateChatHistoryUI();
        this.closeMobileMenu();
    }

    updateChatHistoryUI() {
        this.elements.chatHistory.innerHTML = '';

        if (this.chatHistory.length === 0) {
            const emptyState = document.createElement('div');
            emptyState.className = 'empty-state';
            emptyState.innerHTML = `
                <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">💬</div>
                    <div>No chats yet</div>
                    <div style="font-size: 0.875rem;">Start a conversation!</div>
                </div>
            `;
            this.elements.chatHistory.appendChild(emptyState);
            return;
        }

        this.chatHistory.forEach(chat => {
            const chatItem = document.createElement('div');
            chatItem.className = 'chat-history-item';
            if (chat.id === this.currentChatId) {
                chatItem.classList.add('active');
            }

            const lastMessage = chat.messages[chat.messages.length - 1];
            const timeAgo = this.getTimeAgo(chat.timestamp);

            chatItem.innerHTML = `
                <div class="chat-title">${chat.title}</div>
                <div class="chat-preview">${chat.messages.length} messages • ${timeAgo}</div>
                ${lastMessage ? `<div class="chat-last-message">${lastMessage.text.substring(0, 50)}${lastMessage.text.length > 50 ? '...' : ''}</div>` : ''}
            `;

            chatItem.addEventListener('click', () => this.loadChat(chat.id));
            this.elements.chatHistory.appendChild(chatItem);
        });
    }

    getTimeAgo(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (days > 0) return `${days}d ago`;
        if (hours > 0) return `${hours}h ago`;
        if (minutes > 0) return `${minutes}m ago`;
        return 'Just now';
    }

    // Mobile Menu Management
    toggleMobileMenu() {
        this.elements.sidebar.classList.toggle('open');
    }

    closeMobileMenu() {
        this.elements.sidebar.classList.remove('open');
    }

    // ===== USER SESSION MANAGEMENT =====

    getCurrentUser() {
        try {
            // Check session storage first (temporary login)
            let sessionData = sessionStorage.getItem('currentSession');
            if (sessionData) {
                return JSON.parse(sessionData);
            }

            // Check local storage (remember me)
            sessionData = localStorage.getItem('currentSession');
            if (sessionData) {
                return JSON.parse(sessionData);
            }

            return null;
        } catch (error) {
            console.error('Error getting current user:', error);
            return null;
        }
    }

    createDemoUser() {
        console.log('🎭 Creating demo user session...');

        // Create a demo user
        const demoUser = {
            username: 'DemoUser',
            email: '<EMAIL>',
            avatar: '👤',
            loginTime: new Date().toISOString(),
            isDemo: true
        };

        // Save to session storage
        sessionStorage.setItem('currentSession', JSON.stringify(demoUser));

        // Update current user
        this.currentUser = demoUser;

        // Update display
        this.updateUserDisplay();

        console.log('✅ Demo user created:', demoUser.username);
    }

    redirectToLogin() {
        console.log('No user session found, redirecting to login...');
        window.location.href = 'login.html';
    }

    openProfile() {
        console.log('👤 Opening user profile...');

        if (!this.currentUser) {
            this.showToast('Please log in to access your profile', 'error');
            return;
        }

        // For demo purposes, show profile info in a toast
        if (this.currentUser.isDemo) {
            this.showProfileInfo();
        } else {
            // Try to navigate to profile page
            try {
                window.location.href = 'profile.html';
            } catch (error) {
                console.error('Profile page error:', error);
                this.showProfileInfo();
            }
        }
    }

    showProfileInfo() {
        const profileInfo = `
👤 **Profile Information**

**Username:** ${this.currentUser.username}
**Email:** ${this.currentUser.email || 'Not set'}
**Login Time:** ${new Date(this.currentUser.loginTime).toLocaleString()}
**Status:** ${this.currentUser.isDemo ? 'Demo User' : 'Registered User'}

Profile management coming soon! 🚀
        `;

        this.displayMessage(profileInfo, 'ai');
        this.showToast('Profile info displayed in chat! 👤', 'info');
    }

    // ===== TOOLS MENU SYSTEM =====

    setupToolsMenu() {
        console.log('🛠️ Setting up tools menu...');

        // FIXED: Improved tools menu with better event handling
        if (this.elements.toolsBtn) {
            this.elements.toolsBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🛠️ Tools button clicked');
                this.toggleToolsPanel();
            });
        }

        // Add click handlers for tool items
        document.addEventListener('click', (e) => {
            const toolItem = e.target.closest('.tool-item');
            if (toolItem) {
                const toolName = toolItem.dataset.tool;
                console.log('🛠️ Tool clicked:', toolName);
                this.handleToolClick(toolName);
            }
        });

        // Close tools panel when clicking outside
        document.addEventListener('click', (e) => {
            const toolsMenu = e.target.closest('.tools-menu');
            if (!toolsMenu && this.elements.toolsPanel) {
                this.hideToolsPanel();
            }
        });

        console.log('🛠️ Tools menu initialized successfully');
    }

    toggleToolsPanel() {
        if (this.elements.toolsPanel) {
            const isVisible = this.elements.toolsPanel.style.opacity === '1';
            if (isVisible) {
                this.hideToolsPanel();
            } else {
                this.showToolsPanel();
            }
        }
    }

    showToolsPanel() {
        if (this.elements.toolsPanel) {
            this.elements.toolsPanel.style.opacity = '1';
            this.elements.toolsPanel.style.visibility = 'visible';
            this.elements.toolsPanel.style.transform = 'translateY(0) scale(1)';
            console.log('🛠️ Tools panel shown');
        }
    }

    hideToolsPanel() {
        if (this.elements.toolsPanel) {
            this.elements.toolsPanel.style.opacity = '0';
            this.elements.toolsPanel.style.visibility = 'hidden';
            this.elements.toolsPanel.style.transform = 'translateY(-10px) scale(0.95)';
            console.log('🛠️ Tools panel hidden');
        }
    }

    handleToolClick(toolName) {
        console.log(`🛠️ Tool clicked: ${toolName}`);

        // Close tools panel
        this.hideToolsPanel();

        switch (toolName) {
            case 'calculator':
                this.openCalculator();
                break;
            case 'notes':
                this.openNotes();
                break;
            case 'reminders':
                this.openReminders();
                break;
            case 'memory':
                this.openMemoryViewer();
                break;
            case 'todo':
                this.openTodoList();
                break;
            case 'settings':
                this.openSettings();
                break;
            default:
                this.showToast(`${toolName} tool coming soon! 🚀`);
        }
    }

    openCalculator() {
        console.log('🧮 Opening calculator...');

        try {
            // Check if modal exists
            const modal = document.getElementById('calculator-modal');
            if (!modal) {
                console.error('❌ Calculator modal not found in DOM');
                this.showToast('Calculator not available - please refresh the page', 'error');
                return;
            }

            this.showCalculatorModal();
        } catch (error) {
            console.error('❌ Error opening calculator:', error);
            this.showToast('Error opening calculator', 'error');
        }
    }

    showCalculatorModal() {
        const modal = document.getElementById('calculator-modal');
        if (modal) {
            modal.style.display = 'flex';
            modal.style.opacity = '1';

            // Setup calculator functionality if not already done
            this.setupCalculatorFunctionality();

            console.log('✅ Calculator modal opened');
        }
    }

    setupCalculatorFunctionality() {
        // Basic calculator setup
        const closeBtn = document.getElementById('calculator-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideCalculatorModal();
            });
        }

        // Add calculator button functionality
        const calcButtons = document.querySelectorAll('.calc-btn');
        calcButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const value = e.target.dataset.value;
                this.handleCalculatorInput(value);
            });
        });
    }

    hideCalculatorModal() {
        const modal = document.getElementById('calculator-modal');
        if (modal) {
            modal.style.display = 'none';
            modal.style.opacity = '0';
            console.log('✅ Calculator modal closed');
        }
    }

    handleCalculatorInput(value) {
        const display = document.getElementById('calc-result');
        if (!display) return;

        if (value === 'AC') {
            display.value = '';
        } else if (value === 'C') {
            display.value = display.value.slice(0, -1);
        } else if (value === '=') {
            try {
                display.value = eval(display.value.replace('×', '*').replace('−', '-'));
            } catch (error) {
                display.value = 'Error';
            }
        } else {
            display.value += value;
        }
    }

    openNotes() {
        this.showToast('📋 Notes tool coming soon!');
        // TODO: Implement notes modal
    }

    openReminders() {
        this.showToast('⏰ Reminders tool coming soon!');
        // TODO: Implement reminders modal
    }

    openMemoryViewer() {
        this.showToast('🧠 Memory viewer coming soon!');
        // TODO: Implement memory viewer modal
    }

    openTodoList() {
        this.showToast('🧾 To-Do list coming soon!');
        // TODO: Implement todo list modal
    }

    openSettings() {
        this.showToast('⚙️ Settings tool coming soon!');
        // TODO: Implement settings modal or redirect to settings page
    }

    logout() {
        if (this.currentUser && this.currentUser.isDemo) {
            // For demo users, just show a message
            this.showToast('Demo session ended. Refresh to start a new session! 👋', 'info');

            // Clear demo session
            sessionStorage.removeItem('currentSession');
            this.currentUser = null;

            // Update display
            if (this.elements.userName) {
                this.elements.userName.textContent = 'Demo User';
            }

            // Create new demo user after a short delay
            setTimeout(() => {
                this.createDemoUser();
            }, 2000);

        } else {
            // Clear all session data for real users
            sessionStorage.removeItem('currentSession');
            localStorage.removeItem('currentSession');
            localStorage.removeItem('rememberedUser');

            // Redirect to login
            this.redirectToLogin();
        }
    }

    updateUserDisplay() {
        // FIXED: Improved user display with better error handling and fallbacks
        console.log('👤 Updating user display...', this.currentUser);

        if (this.currentUser) {
            // Update username display
            if (this.elements.userName) {
                this.elements.userName.textContent = this.currentUser.username || 'User';
                console.log('✅ Username updated:', this.currentUser.username);
            } else {
                console.warn('⚠️ Username element not found');
            }

            // Update user indicator tooltip
            if (this.elements.userIndicator) {
                this.elements.userIndicator.title = `Logged in as ${this.currentUser.username || 'User'}`;
            }

            // Update avatar if available
            const avatarElement = document.querySelector('.user-avatar');
            if (avatarElement) {
                avatarElement.textContent = this.currentUser.avatar || '👤';
            }

            console.log('✅ User display updated successfully:', this.currentUser.username);
        } else {
            // Fallback for no user
            if (this.elements.userName) {
                this.elements.userName.textContent = 'Demo User';
            }
            console.warn('⚠️ No current user - using fallback display');
        }
    }

    updateImageGeneratorStatus() {
        const config = getConfig();
        const btn = this.elements.imageGeneratorBtn;

        if (config.imageGeneration.enabled) {
            btn.title = `Generate AI Image 🎨 (${config.imageGeneration.provider} API)`;
            btn.style.color = 'var(--primary-color)';
            console.log('🎨 Image generator ACTIVE with', config.imageGeneration.provider, 'API');
        } else {
            btn.title = 'Generate AI Image 🎨 (Demo Mode - Configure API key)';
            btn.style.color = 'var(--text-secondary)';
            console.log('🎨 Image generator in DEMO mode - API key needed');
        }
    }

    updateGroqStatus() {
        const config = getConfig();

        if (config.groq.enabled) {
            console.log('🚀 Groq API ACTIVE with model:', config.groq.defaultModel);

            // Add visual indicator if needed
            const statusElement = document.querySelector('.groq-status');
            if (statusElement) {
                statusElement.textContent = '🚀 Groq AI Active';
                statusElement.style.color = 'var(--success)';
            }
        } else {
            console.log('⚠️ Groq API DISABLED - using fallback responses');

            const statusElement = document.querySelector('.groq-status');
            if (statusElement) {
                statusElement.textContent = '⚠️ Fallback Mode';
                statusElement.style.color = 'var(--warning)';
            }
        }
    }

    updateDeepSeekStatus() {
        const config = getConfig();

        if (config.deepseek.enabled) {
            console.log('🔧 DeepSeek API ACTIVE with model:', config.deepseek.defaultModel);
            console.log('🔧 DeepSeek provider:', config.deepseek.provider);

            // Add visual indicator if needed
            const statusElement = document.querySelector('.deepseek-status');
            if (statusElement) {
                statusElement.textContent = '🔧 DeepSeek Coding AI Active';
                statusElement.style.color = 'var(--success)';
            }
        } else {
            console.log('⚠️ DeepSeek API DISABLED - coding tasks will use Groq fallback');

            const statusElement = document.querySelector('.deepseek-status');
            if (statusElement) {
                statusElement.textContent = '⚠️ Coding Fallback Mode';
                statusElement.style.color = 'var(--warning)';
            }
        }
    }

    // ===== WELCOME MESSAGE SYSTEM =====

    showWelcomeMessage() {
        // Check if user has seen welcome message for this session
        const hasSeenWelcome = sessionStorage.getItem('hasSeenWelcome');
        const lastVersion = localStorage.getItem('lastSeenVersion');
        const currentVersion = 'v1.5';

        if (!hasSeenWelcome) {
            // Delay to ensure UI is loaded
            setTimeout(() => {
                if (lastVersion && lastVersion !== currentVersion) {
                    // Show update message for returning users
                    this.displayUpdateMessage(lastVersion, currentVersion);
                } else if (!lastVersion) {
                    // Show first-time welcome for completely new users
                    this.displayFirstTimeWelcome();
                } else {
                    // Show regular welcome message
                    this.displayWelcomeMessage();
                }

                sessionStorage.setItem('hasSeenWelcome', 'true');
                localStorage.setItem('lastSeenVersion', currentVersion);
            }, 800);
        }
    }

    displayWelcomeMessage() {
        const userName = this.currentUser ? this.currentUser.username : 'there';
        const currentTime = new Date().getHours();

        let greeting;
        if (currentTime < 12) {
            greeting = 'Good morning';
        } else if (currentTime < 17) {
            greeting = 'Good afternoon';
        } else {
            greeting = 'Good evening';
        }

        const welcomeMessage = `
🐾 **${greeting}, ${userName}!** Welcome to **Smokey AI v1.5**

I'm excited to show you what's new in this update:

✨ **New Features:**
• 🛠️ **Tools Menu** - Access Calculator, Notes, Reminders & more
• 🎨 **Enhanced Image Generator** - Create amazing AI artwork
• 👤 **User Profiles** - Manage your account and preferences
• 🧠 **Improved Memory System** - Better conversation context

🎯 **UI Improvements:**
• Cleaner, more focused design
• Wider chat input for better typing experience
• Optimized layout with better space utilization
• Responsive design for all devices

💬 **Ready to Chat:**
I'm here to help with anything you need - from answering questions to creative projects, coding help, or just having a friendly conversation!

What would you like to explore today?
        `;

        this.displayMessage(welcomeMessage, 'ai');

        // Add to memory
        this.addToMemory('session_start', {
            version: 'v1.5',
            greeting: greeting,
            timestamp: new Date().toISOString(),
            features_introduced: ['tools_menu', 'image_generator', 'user_profiles', 'memory_system']
        });

        console.log('👋 Welcome message displayed for', userName);
    }

    displayUpdateMessage(oldVersion, newVersion) {
        const userName = this.currentUser ? this.currentUser.username : 'there';

        const updateMessage = `
🎉 **Welcome back, ${userName}!**

**Smokey AI has been updated from ${oldVersion} to ${newVersion}!**

🆕 **What's New:**
• 🛠️ **Tools Menu** - New 9-dot grid with Calculator, Notes, Reminders & more
• 🎨 **Enhanced Image Generator** - Better AI image creation with multiple providers
• 👤 **User Profile System** - Complete profile management with avatar uploads
• 🧠 **Advanced Memory** - Improved conversation context and history
• 📱 **Cleaner UI** - Removed clutter, bigger chat input, better space usage

🔧 **Improvements:**
• Faster loading and better performance
• Enhanced mobile responsiveness
• Improved error handling and user feedback
• Better security and data management

✨ **Ready to explore the new features?** Try clicking the 🛠️ tools menu in the top-right corner!

How can I help you today?
        `;

        this.displayMessage(updateMessage, 'ai');

        // Add to memory
        this.addToMemory('version_update', {
            from_version: oldVersion,
            to_version: newVersion,
            timestamp: new Date().toISOString(),
            user: userName
        });

        console.log(`🔄 Update message displayed: ${oldVersion} → ${newVersion} for ${userName}`);
    }

    displayFirstTimeWelcome() {
        const userName = this.currentUser ? this.currentUser.username : 'there';
        const currentTime = new Date().getHours();

        let greeting;
        if (currentTime < 12) {
            greeting = 'Good morning';
        } else if (currentTime < 17) {
            greeting = 'Good afternoon';
        } else {
            greeting = 'Good evening';
        }

        const firstTimeMessage = `
🎉 **${greeting}, ${userName}!** Welcome to **Smokey AI v1.5**

I'm thrilled you've joined our community! Let me introduce myself and show you around:

🐾 **About Me:**
I'm Smokey, your intelligent AI companion. I'm here to help with anything you need - from answering questions and solving problems to creative projects and friendly conversations.

🛠️ **Explore My Tools:**
Click the **9-dot grid** (🛠️) in the top-right corner to access:
• 🧮 Calculator - Quick math calculations
• 📋 Notes - Jot down important thoughts
• ⏰ Reminders - Never forget important tasks
• 🧠 Memory Viewer - See our conversation history
• 🧾 To-Do Lists - Stay organized
• ⚙️ Settings - Customize your experience

🎨 **Create Amazing Images:**
Click the **🖼️ image button** next to the chat input to generate AI artwork with just a description!

👤 **Your Profile:**
Click the **👤 profile button** to manage your account, upload an avatar, and customize settings.

💬 **Let's Start Chatting:**
I can help you with:
• 🔧 **Coding & Programming** - Python, JavaScript, debugging, algorithms
• 💡 Answering questions on any topic
• ✍️ Writing and editing content
• 🎨 Creative projects and brainstorming
• 📚 Learning new skills
• 💬 Or just having a friendly conversation!

**What would you like to try first?** Feel free to ask me anything or explore the tools above! 🚀
        `;

        this.displayMessage(firstTimeMessage, 'ai');

        // Add to memory
        this.addToMemory('first_time_user', {
            version: 'v1.5',
            greeting: greeting,
            timestamp: new Date().toISOString(),
            user: userName,
            onboarding_completed: true
        });

        console.log('🌟 First-time welcome message displayed for', userName);
    }

    // Manual welcome trigger (can be called by user commands)
    triggerWelcomeMessage(type = 'manual') {
        switch (type) {
            case 'updates':
                this.displayUpdateMessage('v1.0', 'v1.5');
                break;
            case 'first-time':
                this.displayFirstTimeWelcome();
                break;
            case 'manual':
            default:
                this.displayWelcomeMessage();
                break;
        }
    }

    handleWelcomeCommands(message) {
        const lowerMessage = message.toLowerCase().trim();

        // Welcome command variations
        const welcomeCommands = [
            'welcome', 'hello', 'hi smokey', 'show welcome', 'welcome message',
            'what\'s new', 'whats new', 'updates', 'new features', 'changelog',
            'help', 'tour', 'guide', 'introduction', 'getting started'
        ];

        // Login/Register commands
        const loginCommands = ['login', 'register', 'sign up', 'sign in', 'create account'];

        // Check for login commands
        const isLoginCommand = loginCommands.some(cmd =>
            lowerMessage === cmd ||
            lowerMessage.includes(cmd)
        );

        if (isLoginCommand) {
            this.displayMessage(message, 'user');
            this.handleLoginCommand(lowerMessage);
            return true;
        }

        // Check if message matches any welcome command
        const isWelcomeCommand = welcomeCommands.some(cmd =>
            lowerMessage === cmd ||
            lowerMessage.startsWith(cmd + ' ') ||
            lowerMessage.endsWith(' ' + cmd)
        );

        if (isWelcomeCommand) {
            // Display user message first
            this.displayMessage(message, 'user');

            // Determine which type of welcome to show
            if (lowerMessage.includes('update') || lowerMessage.includes('new') || lowerMessage.includes('changelog')) {
                this.triggerWelcomeMessage('updates');
            } else if (lowerMessage.includes('tour') || lowerMessage.includes('guide') || lowerMessage.includes('getting started')) {
                this.triggerWelcomeMessage('first-time');
            } else {
                this.triggerWelcomeMessage('manual');
            }

            return true;
        }

        return false;
    }

    // ===== DEEPSEEK API INTEGRATION =====

    async getDeepSeekResponse(userInput, taskType = 'code') {
        const config = getConfig();

        if (!config.deepseek.enabled) {
            console.log('🔧 DeepSeek API not enabled, using Groq fallback for coding');
            this.showToast('⚙️ DeepSeek not available, using Groq for coding tasks', 'info');
            return this.getGroqResponse(userInput, config.groq.models.balanced);
        }

        console.log('🔧 Using DeepSeek for coding tasks:', userInput.substring(0, 50) + '...');
        this.showToast('⚙️ Using DeepSeek for coding tasks', 'info');

        try {
            const response = await this.callDeepSeekAPI(userInput, taskType);
            return response;
        } catch (error) {
            console.error('❌ DeepSeek API error:', error);
            console.log('🔄 Falling back to Groq for coding task');
            this.showToast('⚠️ DeepSeek unavailable, using Groq fallback', 'warning');
            return this.getGroqResponse(userInput, config.groq.models.balanced);
        }
    }

    async callDeepSeekAPI(userInput, taskType) {
        const config = getConfig();
        const model = this.selectDeepSeekModel(taskType);
        const endpoint = config.deepseek.endpoints[config.deepseek.provider];

        console.log(`🔧 Calling DeepSeek API with model: ${model}`);

        const requestBody = {
            model: model,
            messages: [
                {
                    role: "system",
                    content: config.deepseek.systemPrompt
                },
                {
                    role: "user",
                    content: userInput
                }
            ],
            temperature: config.deepseek.temperature,
            max_tokens: config.deepseek.maxTokens,
            stream: false
        };

        // Add OpenRouter specific headers if using OpenRouter
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config.deepseek.apiKey}`
        };

        if (config.deepseek.provider === 'openrouter') {
            headers['HTTP-Referer'] = window.location.origin;
            headers['X-Title'] = 'Smokey AI';
        }

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), config.deepseek.timeout);

        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorData = await response.text();
                throw new Error(`DeepSeek API error: ${response.status} - ${errorData}`);
            }

            const data = await response.json();

            if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                throw new Error('Invalid response format from DeepSeek API');
            }

            const aiResponse = data.choices[0].message.content;

            console.log('✅ DeepSeek response received:', aiResponse.substring(0, 100) + '...');

            return aiResponse;

        } catch (error) {
            clearTimeout(timeoutId);

            if (error.name === 'AbortError') {
                throw new Error('DeepSeek API request timed out');
            }

            throw error;
        }
    }

    selectDeepSeekModel(taskType) {
        const config = getConfig();

        switch (taskType) {
            case 'code':
            case 'debug':
            case 'programming':
                return config.deepseek.models.coder;
            case 'technical':
            case 'explanation':
                return config.deepseek.models.v2;
            case 'reasoning':
            case 'algorithm':
                return config.deepseek.models.reasoning;
            default:
                return config.deepseek.models.coder;
        }
    }

    // ===== GROQ API INTEGRATION =====

    async getGroqResponse(userInput, model = null) {
        const config = getConfig();

        if (!config.groq.enabled) {
            console.log('🤖 Groq API not enabled, using fallback');
            return this.getFallbackResponse(userInput);
        }

        console.log('🚀 Getting Groq response for:', userInput.substring(0, 50) + '...');

        try {
            const response = await this.callGroqAPI(userInput, model);
            return response;
        } catch (error) {
            console.error('❌ Groq API error:', error);

            // Try fallback with retry logic
            if (config.groq.retryAttempts > 0) {
                console.log('🔄 Retrying with fallback...');
                await this.delay(config.groq.retryDelay);
                try {
                    return await this.callGroqAPI(userInput, config.groq.models.fast);
                } catch (retryError) {
                    console.error('❌ Retry failed:', retryError);
                }
            }

            return this.getFallbackResponse(userInput);
        }
    }

    async callGroqAPI(userInput, model = null) {
        const config = getConfig();
        const selectedModel = model || config.groq.defaultModel;

        console.log(`🤖 Calling Groq API with model: ${selectedModel}`);

        const requestBody = {
            model: selectedModel,
            messages: [
                {
                    role: "system",
                    content: "You are Smokey AI, a helpful and friendly AI assistant. Provide clear, concise, and helpful responses. Keep responses conversational and engaging."
                },
                {
                    role: "user",
                    content: userInput
                }
            ],
            temperature: config.groq.temperature,
            max_tokens: config.groq.maxTokens,
            stream: false
        };

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), config.groq.timeout);

        try {
            const response = await fetch(config.groq.endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.groq.apiKey}`
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorData = await response.text();
                throw new Error(`Groq API error: ${response.status} - ${errorData}`);
            }

            const data = await response.json();

            if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                throw new Error('Invalid response format from Groq API');
            }

            const aiResponse = data.choices[0].message.content;

            console.log('✅ Groq response received:', aiResponse.substring(0, 100) + '...');

            return aiResponse;

        } catch (error) {
            clearTimeout(timeoutId);

            if (error.name === 'AbortError') {
                throw new Error('Groq API request timed out');
            }

            throw error;
        }
    }

    determineTaskType(userInput) {
        const input = userInput.toLowerCase().trim();

        // Coding task patterns (highest priority for DeepSeek routing)
        const codingPatterns = [
            /\b(code|coding|program|programming|script|scripting)\b/,
            /\b(debug|debugging|error|bug|fix)\b/,
            /\b(function|method|class|variable|array|object)\b/,
            /\b(javascript|python|java|c\+\+|html|css|react|node)\b/,
            /\b(api|database|sql|json|xml)\b/,
            /\b(algorithm|data structure|recursion|loop)\b/,
            /\b(tutorial|learn|how to code|programming help)\b/,
            /\b(syntax|compile|runtime|exception)\b/,
            /```[\s\S]*```/, // Code blocks
            /\bdef\s+\w+\(|\bfunction\s+\w+\(|\bclass\s+\w+/, // Function/class definitions
        ];

        // Technical explanation patterns (for DeepSeek-V2)
        const technicalPatterns = [
            /\b(explain|how does|what is|how to)\b.*\b(algorithm|programming|software|technical)\b/,
            /\b(computer science|software engineering|data structures)\b/,
            /\b(machine learning|ai|artificial intelligence)\b/,
            /\b(database design|system architecture|design patterns)\b/
        ];

        // Simple/Fast task patterns
        const simplePatterns = [
            /^(hi|hello|hey|what's up|how are you)/,
            /^(what is|what's|define|explain briefly)/,
            /^(yes|no|maybe|sure|okay|ok)/,
            /^(thanks|thank you|thx)/,
            /^(help|assist|support)/,
            /\b(quick|simple|short|brief)\b/,
            /\?(.*)?$/  // Questions
        ];

        // Complex task patterns
        const complexPatterns = [
            /\b(write|create|generate|compose|draft)\b.*\b(essay|article|story|report|document)\b/,
            /\b(analyze|review|critique|evaluate)\b/,
            /\b(plan|strategy|roadmap|timeline)\b/,
            /\b(research|investigate|study)\b/
        ];

        // Check for coding tasks first (highest priority)
        if (codingPatterns.some(pattern => pattern.test(input))) {
            // Determine specific coding task type
            if (/\b(debug|debugging|error|bug|fix)\b/.test(input)) {
                return 'debug';
            }
            if (/\b(explain|tutorial|learn|how to)\b/.test(input)) {
                return 'technical';
            }
            if (/\b(algorithm|data structure|recursion)\b/.test(input)) {
                return 'algorithm';
            }
            return 'code';
        }

        // Check for technical explanations
        if (technicalPatterns.some(pattern => pattern.test(input))) {
            return 'technical';
        }

        // Check for complex tasks
        if (complexPatterns.some(pattern => pattern.test(input))) {
            return 'complex';
        }

        // Check for simple tasks
        if (simplePatterns.some(pattern => pattern.test(input))) {
            return 'simple';
        }

        // Default to simple for short inputs
        if (input.length < 100) {
            return 'simple';
        }

        return 'balanced';
    }

    isCodingTask(taskType) {
        const codingTaskTypes = ['code', 'debug', 'programming', 'technical', 'algorithm'];
        return codingTaskTypes.includes(taskType);
    }

    getModelForTask(taskType) {
        const config = getConfig();

        switch (taskType) {
            case 'simple':
                return config.groq.models.fast;
            case 'complex':
                return config.groq.models.complex;
            case 'balanced':
            default:
                return config.groq.models.balanced;
        }
    }

    getFallbackResponse(userInput) {
        const fallbackResponses = [
            "I'm here to help! Could you please rephrase your question?",
            "That's an interesting question! Let me think about that...",
            "I'd be happy to assist you with that. Could you provide more details?",
            "Thanks for your message! I'm processing your request...",
            "I understand you're asking about that topic. Let me help you!",
            "Great question! I'm working on getting you the best answer.",
            "I appreciate your patience while I process your request.",
            "That's a thoughtful question. Let me provide you with some insights."
        ];

        const randomResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];
        return randomResponse;
    }

    handleLoginCommand(command) {
        const loginMessage = `
🔐 **Account Management**

You're currently using a **Demo Account** which gives you full access to Smokey AI features!

**Demo Account Benefits:**
• ✅ Full chat functionality
• ✅ Image generation
• ✅ Tools access (Calculator, Notes, etc.)
• ✅ Memory system
• ✅ All features unlocked

**Want a Real Account?**
• 📝 **Register:** [Click here to create an account](register.html)
• 🔑 **Login:** [Click here to login](login.html)

**Real Account Benefits:**
• 💾 Persistent data across devices
• 🔒 Secure password protection
• 📧 Email notifications
• 🎨 Custom avatar uploads
• ⚙️ Advanced settings

For now, enjoy exploring Smokey AI with your demo account! 🚀
        `;

        this.displayMessage(loginMessage, 'ai');
    }

    // ===== CALCULATOR SYSTEM =====

    showCalculatorModal() {
        const modal = document.getElementById('calculator-modal');
        const closeBtn = document.getElementById('calculator-close-btn');

        if (!modal) {
            console.error('❌ Calculator modal element not found');
            return;
        }

        console.log('✅ Calculator modal found, showing...');

        // Show modal with animation
        modal.style.display = 'flex';

        // Force reflow for animation
        modal.offsetHeight;

        // Add active class for animation
        modal.classList.add('active');

        // Initialize calculator
        this.initializeCalculator();

        // Setup close handlers (remove existing first to prevent duplicates)
        if (closeBtn) {
            closeBtn.removeEventListener('click', this.closeCalculatorModal.bind(this));
            closeBtn.addEventListener('click', this.closeCalculatorModal.bind(this));
        }

        // Close on overlay click
        const overlay = modal.querySelector('.calculator-overlay');
        if (overlay) {
            overlay.removeEventListener('click', this.closeCalculatorModal.bind(this));
            overlay.addEventListener('click', this.closeCalculatorModal.bind(this));
        }

        // Close on Escape key
        document.removeEventListener('keydown', this.handleCalculatorKeydown.bind(this));
        document.addEventListener('keydown', this.handleCalculatorKeydown.bind(this));

        console.log('🧮 Calculator modal opened successfully');
    }

    closeCalculatorModal() {
        const modal = document.getElementById('calculator-modal');

        if (modal) {
            modal.classList.remove('active');

            // Remove event listeners
            document.removeEventListener('keydown', this.handleCalculatorKeydown.bind(this));

            console.log('🧮 Calculator modal closed');
        }
    }

    initializeCalculator() {
        this.calcDisplay = document.getElementById('calc-result');
        this.calcButtons = document.querySelectorAll('.calc-btn');
        this.currentInput = '';
        this.operator = '';
        this.previousInput = '';
        this.shouldResetDisplay = false;

        // Clear display
        this.calcDisplay.value = '0';

        // Add button event listeners
        this.calcButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const value = e.target.dataset.value;
                this.handleCalculatorInput(value);
            });
        });

        console.log('🧮 Calculator initialized');
    }

    handleCalculatorInput(value) {
        switch (value) {
            case 'AC':
                this.clearAll();
                break;
            case 'C':
                this.clearLast();
                break;
            case '=':
                this.calculate();
                break;
            case '+':
            case '-':
            case '*':
            case '/':
            case '%':
                this.setOperator(value);
                break;
            default:
                this.inputNumber(value);
                break;
        }
    }

    clearAll() {
        this.currentInput = '';
        this.operator = '';
        this.previousInput = '';
        this.calcDisplay.value = '0';
        this.shouldResetDisplay = false;
    }

    clearLast() {
        if (this.currentInput.length > 1) {
            this.currentInput = this.currentInput.slice(0, -1);
            this.calcDisplay.value = this.currentInput;
        } else {
            this.currentInput = '';
            this.calcDisplay.value = '0';
        }
    }

    inputNumber(num) {
        if (this.shouldResetDisplay) {
            this.currentInput = '';
            this.shouldResetDisplay = false;
        }

        if (num === '.' && this.currentInput.includes('.')) {
            return;
        }

        this.currentInput += num;
        this.calcDisplay.value = this.currentInput;
    }

    setOperator(op) {
        if (this.currentInput === '') return;

        if (this.previousInput !== '' && this.operator !== '') {
            this.calculate();
        }

        this.operator = op;
        this.previousInput = this.currentInput;
        this.shouldResetDisplay = true;
    }

    calculate() {
        if (this.previousInput === '' || this.currentInput === '' || this.operator === '') {
            return;
        }

        let result;
        const prev = parseFloat(this.previousInput);
        const current = parseFloat(this.currentInput);

        try {
            switch (this.operator) {
                case '+':
                    result = prev + current;
                    break;
                case '-':
                    result = prev - current;
                    break;
                case '*':
                    result = prev * current;
                    break;
                case '/':
                    if (current === 0) {
                        throw new Error('Division by zero');
                    }
                    result = prev / current;
                    break;
                case '%':
                    result = prev % current;
                    break;
                default:
                    return;
            }

            // Format result
            if (result.toString().length > 12) {
                result = parseFloat(result.toFixed(8));
            }

            this.calcDisplay.value = result;
            this.currentInput = result.toString();
            this.operator = '';
            this.previousInput = '';
            this.shouldResetDisplay = true;

        } catch (error) {
            this.calcDisplay.value = 'Error';
            this.currentInput = '';
            this.operator = '';
            this.previousInput = '';
            this.shouldResetDisplay = true;
        }
    }

    handleCalculatorKeydown(e) {
        // Only handle keys when calculator is open
        const modal = document.getElementById('calculator-modal');
        if (!modal.classList.contains('active')) return;

        const key = e.key;

        // Prevent default for calculator keys
        if ('0123456789+-*/.='.includes(key) || key === 'Enter' || key === 'Escape' || key === 'Backspace') {
            e.preventDefault();
        }

        // Handle keyboard input
        if ('0123456789.'.includes(key)) {
            this.handleCalculatorInput(key);
        } else if ('+-*/%'.includes(key)) {
            this.handleCalculatorInput(key);
        } else if (key === 'Enter' || key === '=') {
            this.handleCalculatorInput('=');
        } else if (key === 'Escape') {
            this.closeCalculatorModal();
        } else if (key === 'Backspace') {
            this.clearLast();
        } else if (key === 'Delete') {
            this.clearAll();
        }
    }

    // ===== IMAGE GENERATION SYSTEM =====

    openImageModal() {
        // FIXED: Better image modal handling
        if (this.elements.imageModal) {
            this.elements.imageModal.classList.add('active');

            if (this.elements.imagePrompt) {
                this.elements.imagePrompt.focus();
            }

            this.updatePromptCounter();
            console.log('🎨 Image generation modal opened');
        } else {
            console.error('❌ Image modal element not found');
            this.showToast('Image generator not available', 'error');
        }
    }

    closeImageModal() {
        // FIXED: Better image modal closing with error handling
        if (this.elements.imageModal) {
            this.elements.imageModal.classList.remove('active');
            this.resetImageForm();
            console.log('🎨 Image generation modal closed');
        } else {
            console.error('❌ Image modal element not found for closing');
        }
    }

    resetImageForm() {
        this.elements.imagePrompt.value = '';
        this.elements.imageStyle.value = 'realistic';
        this.elements.imageSize.value = '1024x1024';
        this.elements.imageQuality.value = 'standard';
        this.updatePromptCounter();
        this.setImageGenerationLoading(false);
    }

    updatePromptCounter() {
        const prompt = this.elements.imagePrompt.value;
        const count = prompt.length;
        this.elements.promptCounter.textContent = `${count}/500`;

        // Change color based on length
        if (count > 450) {
            this.elements.promptCounter.style.color = 'var(--danger)';
        } else if (count > 350) {
            this.elements.promptCounter.style.color = 'var(--warning)';
        } else {
            this.elements.promptCounter.style.color = 'var(--text-secondary)';
        }
    }

    setImageGenerationLoading(loading) {
        const btn = this.elements.generateImageBtn;
        const btnText = btn.querySelector('.btn-text');
        const btnSpinner = btn.querySelector('.btn-spinner');

        btn.disabled = loading;

        if (loading) {
            btnText.classList.add('hidden');
            btnSpinner.classList.remove('hidden');
        } else {
            btnText.classList.remove('hidden');
            btnSpinner.classList.add('hidden');
        }
    }

    async generateImage() {
        const prompt = this.elements.imagePrompt.value.trim();
        const style = this.elements.imageStyle.value;
        const size = this.elements.imageSize.value;
        const quality = this.elements.imageQuality.value;

        // Validation
        if (!prompt) {
            this.showToast('Please enter a description for your image', 'error');
            return;
        }

        if (prompt.length < 10) {
            this.showToast('Please provide a more detailed description (at least 10 characters)', 'error');
            return;
        }

        console.log('🎨 Starting image generation:', { prompt, style, size, quality });

        // Show loading state
        this.setImageGenerationLoading(true);

        try {
            // Display user message
            const userMessage = `🎨 Generate image: "${prompt}" (${style}, ${size}, ${quality})`;
            this.displayMessage(userMessage, 'user');

            // Close modal
            this.closeImageModal();

            // Show typing indicator
            this.showTypingIndicator();

            // Call image generation API
            const imageResult = await this.callImageGenerationAPI(prompt, style, size, quality);

            // Hide typing indicator
            this.hideTypingIndicator();

            // Display generated image
            this.displayGeneratedImage(imageResult, prompt, style, size, quality);

            // Save to chat history
            this.saveCurrentChat();

        } catch (error) {
            console.error('Image generation error:', error);
            this.hideTypingIndicator();
            this.displayMessage('Sorry, I encountered an error while generating your image. Please try again.', 'ai');
            this.showToast('Image generation failed. Please try again.', 'error');
        } finally {
            this.setImageGenerationLoading(false);
        }
    }

    async callImageGenerationAPI(prompt, style, size, quality) {
        const config = getConfig();

        if (!config.imageGeneration.enabled) {
            console.warn('🎨 Image generation not enabled, using simulation');
            return await this.simulateImageGeneration(prompt, size, quality);
        }

        // Enhanced prompt with style
        const enhancedPrompt = this.enhancePromptWithStyle(prompt, style);
        console.log('🎨 Enhanced prompt:', enhancedPrompt);
        console.log('🎨 Using provider:', config.imageGeneration.provider);

        try {
            // Try multiple API providers based on configuration
            switch (config.imageGeneration.provider) {
                case 'openai':
                    return await this.callOpenAIAPI(enhancedPrompt, size, quality);
                case 'stability':
                    return await this.callStabilityAPI(enhancedPrompt, size, quality);
                case 'deepai':
                    return await this.callDeepAIAPI(enhancedPrompt, size, quality);
                case 'deepimage':
                    return await this.callDeepImageAPI(enhancedPrompt, size, quality);
                case 'replicate':
                    return await this.callReplicateAPI(enhancedPrompt, size, quality);
                case 'huggingface':
                    return await this.callHuggingFaceAPI(enhancedPrompt, size, quality);
                case 'custom':
                default:
                    return await this.callCustomAPI(enhancedPrompt, size, quality);
            }
        } catch (error) {
            console.error('🎨 API call failed, falling back to simulation:', error);
            // Fallback to simulation if API fails
            return await this.simulateImageGeneration(enhancedPrompt, size, quality);
        }
    }

    async callDeepImageAPI(prompt, size, quality) {
        const config = getConfig();
        const endpoint = config.imageGeneration.endpoints.deepimage;

        console.log('🎨 Calling Deep Image API with prompt:', prompt);

        // Parse size
        const [width, height] = size.split('x').map(Number);

        // Try multiple common API formats for Deep Image services
        const apiFormats = [
            // Format 1: JSON with API key in header
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${config.imageGeneration.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    prompt: prompt,
                    width: width,
                    height: height,
                    quality: quality
                })
            },
            // Format 2: JSON with API key as x-api-key
            {
                method: 'POST',
                headers: {
                    'x-api-key': config.imageGeneration.apiKey,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    prompt: prompt,
                    width: width,
                    height: height,
                    quality: quality
                })
            },
            // Format 3: Form data
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${config.imageGeneration.apiKey}`
                },
                body: (() => {
                    const formData = new FormData();
                    formData.append('prompt', prompt);
                    formData.append('width', width.toString());
                    formData.append('height', height.toString());
                    return formData;
                })()
            }
        ];

        // Try each format until one works
        for (let i = 0; i < apiFormats.length; i++) {
            try {
                console.log(`🎨 Trying Deep Image API format ${i + 1}...`);

                const response = await fetch(endpoint, apiFormats[i]);

                if (!response.ok) {
                    console.warn(`🎨 Format ${i + 1} failed:`, response.status);
                    continue;
                }

                const data = await response.json();
                console.log('🎨 Deep Image Response:', data);

                // Handle different response formats
                let imageUrl;
                if (data.image_url) {
                    imageUrl = data.image_url;
                } else if (data.url) {
                    imageUrl = data.url;
                } else if (data.output_url) {
                    imageUrl = data.output_url;
                } else if (data.result) {
                    imageUrl = data.result;
                } else if (data.data && data.data.url) {
                    imageUrl = data.data.url;
                } else if (data.images && data.images[0]) {
                    imageUrl = data.images[0];
                } else {
                    console.warn(`🎨 Format ${i + 1} - No image URL found in response`);
                    continue;
                }

                return {
                    url: imageUrl,
                    prompt: prompt,
                    size: size,
                    quality: quality,
                    timestamp: new Date().toISOString(),
                    id: Date.now(),
                    provider: 'deepimage'
                };

            } catch (error) {
                console.warn(`🎨 Format ${i + 1} error:`, error.message);
                continue;
            }
        }

        throw new Error('All Deep Image API formats failed');
    }

    async callCustomAPI(prompt, size, quality) {
        const config = getConfig();
        const endpoint = config.imageGeneration.endpoints.custom;

        console.log('🎨 Calling custom API:', endpoint);

        // Parse size
        const [width, height] = size.split('x').map(Number);

        // Try Segmind API format (common for many providers)
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'x-api-key': config.imageGeneration.apiKey,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                prompt: prompt,
                negative_prompt: 'blurry, bad quality, distorted',
                style: 'base',
                samples: 1,
                scheduler: 'UniPC',
                num_inference_steps: 25,
                guidance_scale: 8,
                strength: 1,
                high_noise_frac: 0.8,
                seed: Math.floor(Math.random() * 1000000),
                img_width: width,
                img_height: height,
                refiner: true
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('🎨 API Error:', response.status, errorText);
            throw new Error(`API Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('🎨 API Response:', data);

        // Handle different response formats
        let imageUrl;
        if (data.image) {
            imageUrl = data.image;
        } else if (data.images && data.images[0]) {
            imageUrl = data.images[0];
        } else if (data.url) {
            imageUrl = data.url;
        } else if (data.data && data.data[0] && data.data[0].url) {
            imageUrl = data.data[0].url;
        } else {
            throw new Error('No image URL found in API response');
        }

        return {
            url: imageUrl,
            prompt: prompt,
            size: size,
            quality: quality,
            timestamp: new Date().toISOString(),
            id: Date.now(),
            provider: 'custom'
        };
    }

    async callDeepAIAPI(prompt, size, quality) {
        const config = getConfig();

        console.log('🎨 Calling DeepAI API with prompt:', prompt);

        // Create form data for DeepAI API
        const formData = new FormData();
        formData.append('text', prompt);

        // Add optional parameters based on size and quality
        if (size === '1024x1792' || size === '1792x1024') {
            formData.append('width', size.split('x')[0]);
            formData.append('height', size.split('x')[1]);
        }

        const response = await fetch(config.imageGeneration.endpoints.deepai, {
            method: 'POST',
            headers: {
                'Api-Key': config.imageGeneration.apiKey
            },
            body: formData
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('🎨 DeepAI API Error:', response.status, errorText);
            throw new Error(`DeepAI API Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('🎨 DeepAI Response:', data);

        if (!data.output_url) {
            throw new Error('No image URL in DeepAI response');
        }

        return {
            url: data.output_url,
            prompt: prompt,
            size: size,
            quality: quality,
            timestamp: new Date().toISOString(),
            id: Date.now(),
            provider: 'deepai'
        };
    }

    async callOpenAIAPI(prompt, size, quality) {
        const config = getConfig();

        const response = await fetch(config.imageGeneration.endpoints.openai, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.imageGeneration.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: 'dall-e-3',
                prompt: prompt,
                size: size,
                quality: quality,
                n: 1
            })
        });

        if (!response.ok) {
            throw new Error(`OpenAI API Error: ${response.status}`);
        }

        const data = await response.json();

        return {
            url: data.data[0].url,
            prompt: prompt,
            size: size,
            quality: quality,
            timestamp: new Date().toISOString(),
            id: Date.now(),
            provider: 'openai'
        };
    }

    async callStabilityAPI(prompt, size, quality) {
        const config = getConfig();
        const [width, height] = size.split('x').map(Number);

        const response = await fetch(config.imageGeneration.endpoints.stability, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.imageGeneration.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                text_prompts: [{ text: prompt }],
                cfg_scale: 7,
                width: width,
                height: height,
                samples: 1,
                steps: 30
            })
        });

        if (!response.ok) {
            throw new Error(`Stability API Error: ${response.status}`);
        }

        const data = await response.json();

        // Convert base64 to blob URL
        const base64Image = data.artifacts[0].base64;
        const blob = this.base64ToBlob(base64Image, 'image/png');
        const url = URL.createObjectURL(blob);

        return {
            url: url,
            prompt: prompt,
            size: size,
            quality: quality,
            timestamp: new Date().toISOString(),
            id: Date.now(),
            provider: 'stability'
        };
    }

    async callHuggingFaceAPI(prompt, size, quality) {
        const config = getConfig();

        const response = await fetch(config.imageGeneration.endpoints.huggingface, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.imageGeneration.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                inputs: prompt,
                parameters: {
                    num_inference_steps: 25,
                    guidance_scale: 7.5
                }
            })
        });

        if (!response.ok) {
            throw new Error(`Hugging Face API Error: ${response.status}`);
        }

        const blob = await response.blob();
        const url = URL.createObjectURL(blob);

        return {
            url: url,
            prompt: prompt,
            size: size,
            quality: quality,
            timestamp: new Date().toISOString(),
            id: Date.now(),
            provider: 'huggingface'
        };
    }

    base64ToBlob(base64, mimeType) {
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type: mimeType });
    }

    enhancePromptWithStyle(prompt, style) {
        const styleEnhancements = {
            'realistic': 'photorealistic, high detail, professional photography',
            'digital-art': 'digital art, concept art, artstation trending',
            'anime': 'anime style, manga art, japanese animation',
            'oil-painting': 'oil painting, classical art, fine art',
            'watercolor': 'watercolor painting, soft colors, artistic',
            'sketch': 'pencil sketch, hand drawn, artistic sketch',
            '3d-render': '3D render, CGI, high quality 3D art',
            'pixel-art': 'pixel art, retro gaming style, 8-bit art'
        };

        const enhancement = styleEnhancements[style] || '';
        return enhancement ? `${prompt}, ${enhancement}` : prompt;
    }

    async simulateImageGeneration(prompt, size, quality) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 3000));

        // For demonstration, return a placeholder image
        // Replace this with your actual API call
        const placeholderImages = [
            'https://picsum.photos/1024/1024?random=1',
            'https://picsum.photos/1024/1024?random=2',
            'https://picsum.photos/1024/1024?random=3',
            'https://picsum.photos/1024/1024?random=4',
            'https://picsum.photos/1024/1024?random=5'
        ];

        const randomImage = placeholderImages[Math.floor(Math.random() * placeholderImages.length)];

        return {
            url: randomImage,
            prompt: prompt,
            size: size,
            quality: quality,
            timestamp: new Date().toISOString(),
            id: Date.now()
        };
    }

    displayGeneratedImage(imageResult, originalPrompt, style, size, quality) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ai-message image-message';

        const imageContainer = document.createElement('div');
        imageContainer.className = 'image-container';

        const img = document.createElement('img');
        img.src = imageResult.url;
        img.alt = `Generated image: ${originalPrompt}`;
        img.className = 'generated-image';
        img.loading = 'lazy';

        // Add error handling for image loading
        img.onerror = () => {
            img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5YTNhZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIGZhaWxlZCB0byBsb2FkPC90ZXh0Pjwvc3ZnPg==';
        };

        const imageInfo = document.createElement('div');
        imageInfo.className = 'image-info';
        imageInfo.innerHTML = `
            <strong>🎨 Generated Image</strong><br>
            <strong>Prompt:</strong> ${originalPrompt}<br>
            <strong>Style:</strong> ${style} | <strong>Size:</strong> ${size} | <strong>Quality:</strong> ${quality}
        `;

        const imageActions = document.createElement('div');
        imageActions.className = 'image-actions';
        imageActions.innerHTML = `
            <button class="image-action-btn" onclick="window.open('${imageResult.url}', '_blank')">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                    <polyline points="15,3 21,3 21,9"></polyline>
                    <line x1="10" y1="14" x2="21" y2="3"></line>
                </svg>
                Open Full Size
            </button>
            <button class="image-action-btn" onclick="navigator.clipboard.writeText('${imageResult.url}')">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
                Copy URL
            </button>
            <button class="image-action-btn" onclick="smokeyAI.regenerateImage('${originalPrompt}', '${style}', '${size}', '${quality}')">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="23 4 23 10 17 10"></polyline>
                    <polyline points="1 20 1 14 7 14"></polyline>
                    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
                </svg>
                Regenerate
            </button>
        `;

        imageContainer.appendChild(img);
        messageDiv.appendChild(imageContainer);
        messageDiv.appendChild(imageInfo);
        messageDiv.appendChild(imageActions);

        this.elements.output.appendChild(messageDiv);
        this.scrollToBottom();

        // Add to memory
        this.addToMemory('image_generation', {
            prompt: originalPrompt,
            style: style,
            size: size,
            quality: quality,
            timestamp: new Date().toISOString()
        });

        console.log('🎨 Image displayed successfully');
    }

    regenerateImage(prompt, style, size, quality) {
        // Fill the modal with previous settings
        this.elements.imagePrompt.value = prompt;
        this.elements.imageStyle.value = style;
        this.elements.imageSize.value = size;
        this.elements.imageQuality.value = quality;

        // Open modal and generate
        this.openImageModal();

        // Auto-generate after a short delay
        setTimeout(() => {
            this.generateImage();
        }, 500);
    }

    // ===== MEMORY MANAGEMENT SYSTEM =====

    loadUserMemory() {
        try {
            if (!this.currentUser) return {};

            const userData = this.loadCurrentUserData();
            return userData ? userData.memory || {} : {};
        } catch (error) {
            console.error('Error loading memory:', error);
            return {};
        }
    }

    loadCurrentUserData() {
        try {
            if (!this.currentUser) return null;

            const userData = localStorage.getItem(`user_${this.currentUser.username}`);
            return userData ? JSON.parse(userData) : null;
        } catch (error) {
            console.error('Error loading user data:', error);
            return null;
        }
    }

    saveCurrentUserData(userData) {
        try {
            if (!this.currentUser) return;

            localStorage.setItem(`user_${this.currentUser.username}`, JSON.stringify(userData));
        } catch (error) {
            console.error('Error saving user data:', error);
        }
    }

    saveUserMemory() {
        try {
            if (!this.currentUser) return;

            const userData = this.loadCurrentUserData() || {
                username: this.currentUser.username,
                memory: {},
                savedChats: [],
                settings: { theme: 'dark', notifications: true },
                createdAt: new Date().toISOString(),
                lastLogin: new Date().toISOString()
            };

            userData.memory = this.userMemory;
            this.saveCurrentUserData(userData);
            this.updateMemoryIndicator();
        } catch (error) {
            console.error('Error saving memory:', error);
        }
    }

    updateMemoryIndicator() {
        const memoryCount = Object.keys(this.userMemory).length;
        this.elements.memoryCount.textContent = memoryCount;

        if (memoryCount > 0) {
            this.elements.memoryIndicator.style.background = 'var(--primary-color)';
            this.elements.memoryIndicator.style.color = 'white';
            this.elements.memoryIndicator.title = `${memoryCount} memories stored - Click to view`;
        } else {
            this.elements.memoryIndicator.style.background = 'var(--surface)';
            this.elements.memoryIndicator.style.color = 'var(--text-secondary)';
            this.elements.memoryIndicator.title = 'No memories yet - Tell me about yourself!';
        }
    }

    extractAndSaveMemory(message) {
        const patterns = [
            // Name patterns
            {
                regex: /(?:my name is|i'm|i am|call me)\s+([a-zA-Z]+)/i,
                key: 'name',
                transform: (match) => match[1]
            },
            // Pet patterns
            {
                regex: /(?:i have a|my pet is a|i own a)\s+([a-zA-Z]+)/i,
                key: 'pet',
                transform: (match) => match[1]
            },
            // Age patterns
            {
                regex: /(?:i am|i'm)\s+(\d+)\s+(?:years old|years)/i,
                key: 'age',
                transform: (match) => parseInt(match[1])
            },
            // Location patterns
            {
                regex: /(?:i live in|i'm from|i am from)\s+([a-zA-Z\s]+)/i,
                key: 'location',
                transform: (match) => match[1].trim()
            },
            // Job/Profession patterns
            {
                regex: /(?:i work as|i am a|i'm a|my job is)\s+([a-zA-Z\s]+)/i,
                key: 'profession',
                transform: (match) => match[1].trim()
            },
            // Hobby/Interest patterns
            {
                regex: /(?:i love|i like|i enjoy|i'm into)\s+([a-zA-Z\s,]+)/i,
                key: 'interests',
                transform: (match) => {
                    const interest = match[1].trim();
                    const existing = this.userMemory.interests || [];
                    if (!existing.includes(interest)) {
                        return [...existing, interest];
                    }
                    return existing;
                }
            },
            // Favorite things
            {
                regex: /(?:my favorite|my favourite)\s+([a-zA-Z]+)\s+is\s+([a-zA-Z\s]+)/i,
                key: 'favorites',
                transform: (match) => {
                    const category = match[1];
                    const item = match[2].trim();
                    const existing = this.userMemory.favorites || {};
                    return { ...existing, [category]: item };
                }
            }
        ];

        let memoryUpdated = false;

        patterns.forEach(pattern => {
            const match = message.match(pattern.regex);
            if (match) {
                const value = pattern.transform(match);
                if (value !== undefined && value !== null) {
                    this.userMemory[pattern.key] = value;
                    memoryUpdated = true;
                }
            }
        });

        if (memoryUpdated) {
            this.saveUserMemory();
            this.showMemoryNotification();
        }
    }

    checkMemoryQuestions(message) {
        const memoryQuestions = [
            {
                patterns: [
                    /do you remember my name/i,
                    /what is my name/i,
                    /what's my name/i
                ],
                response: () => {
                    if (this.userMemory.name) {
                        return `Yes! Your name is **${this.userMemory.name}**. 😊`;
                    }
                    return "I don't remember your name yet. You can tell me by saying something like 'My name is [your name]'.";
                }
            },
            {
                patterns: [
                    /do you remember what pet i have/i,
                    /what pet do i have/i,
                    /what's my pet/i
                ],
                response: () => {
                    if (this.userMemory.pet) {
                        return `Yes! You have a **${this.userMemory.pet}**! 🐾`;
                    }
                    return "I don't remember what pet you have. You can tell me by saying something like 'I have a [pet type]'.";
                }
            },
            {
                patterns: [
                    /what do you remember about me/i,
                    /what do you know about me/i,
                    /tell me what you remember/i
                ],
                response: () => this.getMemorySummary()
            },
            {
                patterns: [
                    /what do i like/i,
                    /what are my interests/i,
                    /what am i into/i
                ],
                response: () => {
                    if (this.userMemory.interests && this.userMemory.interests.length > 0) {
                        return `Based on what you've told me, you like:\n\n${this.userMemory.interests.map(interest => `- ${interest}`).join('\n')}\n\nPretty cool interests! 😊`;
                    }
                    return "I don't remember your interests yet. You can tell me by saying something like 'I love [activity]' or 'I'm into [hobby]'.";
                }
            }
        ];

        for (const question of memoryQuestions) {
            for (const pattern of question.patterns) {
                if (pattern.test(message)) {
                    return question.response();
                }
            }
        }

        return null;
    }

    handleMemoryCommands(message) {
        // Handle /memory command
        if (message.toLowerCase() === '/memory') {
            this.displayMessage('/memory', 'user');
            this.displayMessage(this.getMemorySummary(), 'ai');
            return true;
        }

        // Handle /forget command
        const forgetMatch = message.match(/^\/forget\s+(.+)$/i);
        if (forgetMatch) {
            const key = forgetMatch[1].toLowerCase();
            this.displayMessage(message, 'user');

            if (this.userMemory[key]) {
                delete this.userMemory[key];
                this.saveUserMemory();
                this.displayMessage(`I've forgotten your **${key}**. 🗑️`, 'ai');
            } else {
                this.displayMessage(`I don't have any memory about your **${key}**.`, 'ai');
            }
            return true;
        }

        // Handle /clear-memory command
        if (message.toLowerCase() === '/clear-memory') {
            this.displayMessage('/clear-memory', 'user');

            if (Object.keys(this.userMemory).length > 0) {
                this.userMemory = {};
                this.saveUserMemory();
                this.displayMessage('All memories cleared! Starting fresh. 🧠✨', 'ai');
            } else {
                this.displayMessage('No memories to clear!', 'ai');
            }
            return true;
        }

        return false;
    }

    getMemorySummary() {
        if (Object.keys(this.userMemory).length === 0) {
            return `I don't have any memories about you yet! 🤔

**Tell me about yourself:**
- "My name is [your name]"
- "I have a [pet type]"
- "I love [hobby/interest]"
- "I work as a [profession]"
- "I live in [location]"

**Memory Commands:**
- \`/memory\` - Show all memories
- \`/forget [key]\` - Delete specific memory
- \`/clear-memory\` - Clear all memories`;
        }

        let summary = "Here's what I remember about you:\n\n";

        if (this.userMemory.name) {
            summary += `**Name:** ${this.userMemory.name}\n`;
        }

        if (this.userMemory.age) {
            summary += `**Age:** ${this.userMemory.age} years old\n`;
        }

        if (this.userMemory.location) {
            summary += `**Location:** ${this.userMemory.location}\n`;
        }

        if (this.userMemory.profession) {
            summary += `**Profession:** ${this.userMemory.profession}\n`;
        }

        if (this.userMemory.pet) {
            summary += `**Pet:** ${this.userMemory.pet} 🐾\n`;
        }

        if (this.userMemory.interests && this.userMemory.interests.length > 0) {
            summary += `**Interests:** ${this.userMemory.interests.join(', ')}\n`;
        }

        if (this.userMemory.favorites && Object.keys(this.userMemory.favorites).length > 0) {
            summary += `**Favorites:**\n`;
            Object.entries(this.userMemory.favorites).forEach(([category, item]) => {
                summary += `  - ${category}: ${item}\n`;
            });
        }

        summary += `\n**Memory Commands:**
- \`/memory\` - Show this summary
- \`/forget [key]\` - Delete specific memory
- \`/clear-memory\` - Clear all memories`;

        return summary;
    }

    enhanceMessageWithMemory(message) {
        if (Object.keys(this.userMemory).length === 0) {
            return message;
        }

        let context = "Context about the user: ";
        const contextParts = [];

        if (this.userMemory.name) {
            contextParts.push(`name is ${this.userMemory.name}`);
        }

        if (this.userMemory.pet) {
            contextParts.push(`has a ${this.userMemory.pet}`);
        }

        if (this.userMemory.interests && this.userMemory.interests.length > 0) {
            contextParts.push(`likes ${this.userMemory.interests.join(', ')}`);
        }

        if (this.userMemory.profession) {
            contextParts.push(`works as ${this.userMemory.profession}`);
        }

        if (contextParts.length > 0) {
            context += contextParts.join(', ') + ". ";
            return context + "User message: " + message;
        }

        return message;
    }

    showMemoryNotification() {
        this.showToast('💾 Memory updated! I\'ll remember that.', 'success');
    }
}

// Global function for copying code
function copyCode(button) {
    const codeBlock = button.closest('.code-block');
    const code = codeBlock.querySelector('code').textContent;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(code).then(() => {
            // Show success feedback
            const originalText = button.innerHTML;
            button.innerHTML = `
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="20,6 9,17 4,12"></polyline>
                </svg>
            `;
            button.style.color = 'var(--success)';

            setTimeout(() => {
                button.innerHTML = originalText;
                button.style.color = '';
            }, 2000);
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = code;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
    }
    // Debug function - can be called from console
    testCalculator() {
        console.log('🧮 Testing calculator...');
        this.openCalculator();
    }

    // Test Groq API function
    async testGroq(message = "Hello, how are you?") {
        console.log('🚀 Testing Groq API...');
        try {
            const response = await this.getGroqResponse(message);
            console.log('✅ Groq response:', response);
            this.displayMessage(`Test: ${message}`, 'user');
            this.displayMessage(response, 'ai');
            return response;
        } catch (error) {
            console.error('❌ Groq test failed:', error);
            return null;
        }
    }

    // Test DeepSeek API function
    async testDeepSeek(message = "Write a simple Python function to calculate factorial") {
        console.log('🔧 Testing DeepSeek API...');
        try {
            const response = await this.getDeepSeekResponse(message, 'code');
            console.log('✅ DeepSeek response:', response);
            this.displayMessage(`Test: ${message}`, 'user');
            this.displayMessage(response, 'ai');
            return response;
        } catch (error) {
            console.error('❌ DeepSeek test failed:', error);
            return null;
        }
    }
}

// Global variable for debugging
let smokeyApp;

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    smokeyApp = new SmokeyAI();

    // Make test functions available globally
    window.testCalculator = () => smokeyApp.testCalculator();
    window.testGroq = (message) => smokeyApp.testGroq(message);
    window.testDeepSeek = (message) => smokeyApp.testDeepSeek(message);
});
