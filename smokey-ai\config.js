// Smokey AI v1 Configuration
// This file contains API keys and configuration settings

const CONFIG = {
    // Image Generation API Settings
    imageGeneration: {
        // Your API key
        apiKey: 'f7c22b80-3d79-11f0-b7b2-31db9926ded4',

        // API Provider (options: 'openai', 'stability', 'deepimage', 'custom')
        provider: 'deepimage',

        // API Endpoints
        endpoints: {
            openai: 'https://api.openai.com/v1/images/generations',
            stability: 'https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image',
            deepai: 'https://api.deepai.org/api/text2img',
            deepimage: 'https://api.deepimage.ai/v1/generate', // Generic endpoint - will be updated
            replicate: 'https://api.replicate.com/v1/predictions',
            huggingface: 'https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-xl-base-1.0',
            custom: 'https://api.segmind.com/v1/sd1.5-txt2img'
        },

        // Default settings
        defaults: {
            model: 'dall-e-3',
            size: '1024x1024',
            quality: 'standard',
            style: 'realistic'
        },

        // Rate limiting
        rateLimit: {
            maxRequestsPerMinute: 5,
            maxRequestsPerHour: 50
        }
    },

    // Chat API Settings (for future use)
    chat: {
        apiKey: 'YOUR_CHAT_API_KEY_HERE',
        provider: 'openai',
        model: 'gpt-3.5-turbo',
        maxTokens: 2048
    },

    // Groq API Configuration
    // Get your free API key from: https://console.groq.com/keys
    groq: {
        enabled: true,
        apiKey: '********************************************************', // Your Groq API key
        endpoint: 'https://api.groq.com/openai/v1/chat/completions',
        models: {
            fast: 'llama3-8b-8192',         // For quick responses
            balanced: 'mixtral-8x7b-32768', // For balanced tasks
            complex: 'llama3-70b-8192'      // For complex reasoning
        },
        defaultModel: 'llama3-8b-8192',
        temperature: 0.7,
        maxTokens: 1024,
        timeout: 10000, // 10 seconds
        retryAttempts: 2,
        retryDelay: 1000 // 1 second
    },

    // DeepSeek API Configuration
    // Get your API key from: https://platform.deepseek.com/api_keys or https://openrouter.ai/keys
    // For production, use environment variables: process.env.DEEPSEEK_API_KEY
    deepseek: {
        enabled: true, // Enabled with valid API key
        apiKey: 'sk-8a7d174951f44b0b859f49d5b11b2052', // Your DeepSeek API key
        provider: 'official', // 'openrouter' or 'official'
        endpoints: {
            openrouter: 'https://openrouter.ai/api/v1/chat/completions',
            official: 'https://api.deepseek.com/v1/chat/completions'
        },
        models: {
            coder: 'deepseek-coder',     // For coding tasks
            v2: 'deepseek-chat',         // For technical explanations
            reasoning: 'deepseek-reasoner' // For complex reasoning
        },
        defaultModel: 'deepseek-coder',
        temperature: 0.2, // Lower temperature for more precise code
        maxTokens: 2048,
        timeout: 15000, // 15 seconds for complex code generation
        retryAttempts: 2,
        retryDelay: 1500,
        systemPrompt: "You are DeepSeek, an expert programming assistant. Provide clear, efficient, and well-documented code solutions."
    },

    // Application Settings
    app: {
        version: 'v1.5',
        name: 'Smokey AI',
        maxChatHistory: 100,
        maxMemoryItems: 500,
        autoSave: true,
        debugMode: false
    },

    // UI Settings
    ui: {
        theme: 'dark',
        animations: true,
        soundEffects: false,
        notifications: true
    }
};

// API Key validation
function validateApiKey(key) {
    if (!key || key === 'YOUR_API_KEY_HERE') {
        console.warn('⚠️ API key not configured. Please set your API key in config.js');
        return false;
    }

    if (key.length < 10) {
        console.warn('⚠️ API key appears to be invalid (too short)');
        return false;
    }

    return true;
}

// Get configuration with validation
function getConfig() {
    // Validate image generation API key
    if (!validateApiKey(CONFIG.imageGeneration.apiKey)) {
        CONFIG.imageGeneration.enabled = false;
    } else {
        CONFIG.imageGeneration.enabled = true;
    }

    // Validate Groq API key
    if (!validateGroqApiKey(CONFIG.groq.apiKey)) {
        CONFIG.groq.enabled = false;
    } else {
        CONFIG.groq.enabled = true;
    }

    // Validate DeepSeek API key
    if (!validateDeepSeekApiKey(CONFIG.deepseek.apiKey)) {
        CONFIG.deepseek.enabled = false;
    } else {
        CONFIG.deepseek.enabled = true;
    }

    return CONFIG;
}

// Groq API key validation
function validateGroqApiKey(key) {
    if (!key || key === 'gsk_YOUR_GROQ_API_KEY_HERE') {
        console.warn('⚠️ Groq API key not configured. Using fallback responses.');
        return false;
    }

    if (!key.startsWith('gsk_') || key.length < 20) {
        console.warn('⚠️ Groq API key appears to be invalid format');
        return false;
    }

    return true;
}

// DeepSeek API key validation
function validateDeepSeekApiKey(key) {
    if (!key || key === 'YOUR_DEEPSEEK_API_KEY_HERE') {
        console.warn('⚠️ DeepSeek API key not configured. Coding tasks will use fallback.');
        return false;
    }

    // DeepSeek keys can be from OpenRouter (sk_or_) or official DeepSeek API
    if (key.startsWith('sk_or_') || key.startsWith('sk-') || key.length > 20) {
        return true;
    }

    console.warn('⚠️ DeepSeek API key appears to be invalid format');
    return false;
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, getConfig, validateApiKey };
} else {
    window.CONFIG = CONFIG;
    window.getConfig = getConfig;
    window.validateApiKey = validateApiKey;
}

console.log('🔧 Configuration loaded:', {
    version: CONFIG.app.version,
    imageGeneration: CONFIG.imageGeneration.enabled ? 'enabled' : 'disabled',
    imageProvider: CONFIG.imageGeneration.provider,
    groq: CONFIG.groq.enabled ? 'enabled' : 'disabled',
    groqModel: CONFIG.groq.defaultModel,
    deepseek: CONFIG.deepseek.enabled ? 'enabled' : 'disabled',
    deepseekModel: CONFIG.deepseek.defaultModel,
    deepseekProvider: CONFIG.deepseek.provider
});
