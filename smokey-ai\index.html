<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smokey AI v1.5 - Smart Assistant</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2M21 9V7L15 1L9 7V9C9 10.1 9.9 11 11 11V16L12 17L13 16V11C14.1 11 15 10.1 15 9M12 4.5C11.2 4.5 10.5 5.2 10.5 6S11.2 7.5 12 7.5 13.5 6.8 13.5 6 12.8 4.5 12 4.5Z' fill='%2380cbc4'/%3E%3C/svg%3E" type="image/svg+xml">
</head>
<body>
    <!-- Mobile Menu Toggle -->
    <button class="mobile-menu-toggle" id="mobile-menu-toggle">
        <span></span>
        <span></span>
        <span></span>
    </button>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span class="logo-icon">🐾</span>
                <h1>Smokey AI</h1>
                <span class="beta-badge">v1.5</span>
            </div>
            <div class="header-controls">
                <div class="header-actions">
                    <!-- Tools Menu -->
                    <div class="tools-menu" id="tools-menu">
                        <button class="tools-btn" id="tools-btn" title="Tools">
                            <div class="grid-icon">
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                            </div>
                        </button>

                        <!-- Tools Panel -->
                        <div class="tools-panel" id="tools-panel">
                            <div class="tools-grid">
                                <div class="tool-item" data-tool="calculator">
                                    <div class="tool-icon">🧮</div>
                                    <div class="tool-label">Calculator</div>
                                </div>
                                <div class="tool-item" data-tool="notes">
                                    <div class="tool-icon">📋</div>
                                    <div class="tool-label">Notes</div>
                                </div>
                                <div class="tool-item" data-tool="reminders">
                                    <div class="tool-icon">⏰</div>
                                    <div class="tool-label">Reminders</div>
                                </div>
                                <div class="tool-item" data-tool="memory">
                                    <div class="tool-icon">🧠</div>
                                    <div class="tool-label">Memory</div>
                                </div>
                                <div class="tool-item" data-tool="todo">
                                    <div class="tool-icon">🧾</div>
                                    <div class="tool-label">To-Do</div>
                                </div>
                                <div class="tool-item" data-tool="settings">
                                    <div class="tool-icon">⚙️</div>
                                    <div class="tool-label">Settings</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Indicator -->
                    <div class="user-indicator" id="user-indicator" title="Current user">
                        <button class="profile-btn" id="profile-btn" title="User Profile">
                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                        </button>
                        <div class="user-avatar">👤</div>
                        <span class="user-name" id="user-name">Loading...</span>
                        <button class="logout-btn" id="logout-btn" title="Logout">
                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="memory-indicator" id="memory-indicator" title="Memory status">
                    <div class="memory-icon">🧠</div>
                    <span class="memory-count" id="memory-count">0</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span class="status-text">Online</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <main class="main-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2>Chat History</h2>
                <div class="sidebar-actions">
                    <button class="new-chat-btn" id="new-chat">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 5v14M5 12h14"></path>
                        </svg>
                        New Chat
                    </button>
                    <button class="clear-history-btn" id="clear-history" title="Clear all chat history">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14zM10 11v6M14 11v6"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="chat-history" id="chat-history">
                <!-- Chat history items will be populated here -->
            </div>
            <div class="sidebar-footer">
                <div class="chat-stats" id="chat-stats">
                    <span class="stat-item">
                        <span class="stat-label">Total Chats:</span>
                        <span class="stat-value" id="total-chats">0</span>
                    </span>
                    <span class="stat-item">
                        <span class="stat-label">Messages:</span>
                        <span class="stat-value" id="total-messages">0</span>
                    </span>
                </div>
            </div>
        </aside>

        <!-- Chat Container -->
        <section class="chat-container" id="chat-container">
            <!-- Welcome Message -->
            <div class="welcome-message" id="welcome-message">
                <div class="welcome-content">
                    <div class="welcome-icon">🐾</div>
                    <h2>Hello! I'm Smokey AI</h2>
                    <p>I'm your smart assistant, named after my creator's cat. I'm here to help you with anything you need!</p>

                </div>
            </div>

            <!-- Chat Output -->
            <div class="chat-output" id="output">
                <!-- Messages will appear here -->
            </div>



            <!-- Input Area -->
            <div class="input-area" id="input-area">
                <div class="input-container">
                    <textarea
                        id="input"
                        placeholder="Ask me anything... (Press Shift+Enter for new line)"
                        autocomplete="off"
                        maxlength="1000"
                        rows="1"
                    ></textarea>
                    <div class="input-actions">
                        <button class="action-button" id="attach-button" title="Attach file (coming soon)">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
                            </svg>
                        </button>
                        <button class="action-button" id="image-generator-btn" title="Generate AI Image 🎨">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                <polyline points="21,15 16,10 5,21"></polyline>
                            </svg>
                        </button>
                        <button class="send-button" id="send-button" title="Send message (Enter)">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="22" y1="2" x2="11" y2="13"></line>
                                <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="input-footer">
                    <div class="input-info">
                        <span class="typing-indicator" id="typing-indicator">Smokey is typing...</span>
                        <span class="character-count" id="character-count">0/1000</span>
                    </div>
                    <div class="input-shortcuts">
                        <span class="shortcut">Enter to send</span>
                        <span class="shortcut">Shift+Enter for new line</span>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Image Generation Modal -->
    <div class="image-modal" id="image-modal">
        <div class="image-modal-content">
            <div class="image-modal-header">
                <h3>🎨 AI Image Generator</h3>
                <button class="close-modal-btn" id="close-image-modal">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>

            <div class="image-modal-body">
                <div class="image-prompt-section">
                    <label for="image-prompt">Describe the image you want to generate:</label>
                    <textarea
                        id="image-prompt"
                        placeholder="A beautiful sunset over mountains, digital art style..."
                        rows="3"
                        maxlength="500"
                    ></textarea>
                    <div class="prompt-counter">
                        <span id="prompt-counter">0/500</span>
                    </div>
                </div>

                <div class="image-settings">
                    <div class="setting-group">
                        <label for="image-style">Style:</label>
                        <select id="image-style">
                            <option value="realistic">Realistic</option>
                            <option value="digital-art">Digital Art</option>
                            <option value="anime">Anime</option>
                            <option value="oil-painting">Oil Painting</option>
                            <option value="watercolor">Watercolor</option>
                            <option value="sketch">Sketch</option>
                            <option value="3d-render">3D Render</option>
                            <option value="pixel-art">Pixel Art</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label for="image-size">Size:</label>
                        <select id="image-size">
                            <option value="1024x1024">Square (1024x1024)</option>
                            <option value="1024x1792">Portrait (1024x1792)</option>
                            <option value="1792x1024">Landscape (1792x1024)</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label for="image-quality">Quality:</label>
                        <select id="image-quality">
                            <option value="standard">Standard</option>
                            <option value="hd">HD (Higher Quality)</option>
                        </select>
                    </div>
                </div>

                <div class="image-examples">
                    <h4>💡 Example prompts:</h4>
                    <div class="example-prompts">
                        <button class="example-prompt" data-prompt="A majestic dragon flying over a medieval castle, fantasy art">🐉 Fantasy Dragon</button>
                        <button class="example-prompt" data-prompt="A futuristic city with flying cars and neon lights, cyberpunk style">🌃 Cyberpunk City</button>
                        <button class="example-prompt" data-prompt="A peaceful forest with sunlight filtering through trees, realistic photography">🌲 Forest Scene</button>
                        <button class="example-prompt" data-prompt="A cute robot playing with a cat, cartoon style">🤖 Robot & Cat</button>
                    </div>
                </div>
            </div>

            <div class="image-modal-footer">
                <button class="cancel-btn" id="cancel-image-generation">Cancel</button>
                <button class="generate-btn" id="generate-image-btn">
                    <span class="btn-text">Generate Image</span>
                    <span class="btn-spinner hidden">
                        <svg class="spinner" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.25"></circle>
                            <path fill="currentColor" opacity="0.75" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Generating...
                    </span>
                </button>
            </div>
        </div>
    </div>

    <!-- Calculator Modal -->
    <div id="calculator-modal" class="calculator-modal">
        <div class="calculator-overlay"></div>
        <div class="calculator-container">
            <!-- Calculator Header -->
            <div class="calculator-header">
                <div class="calculator-title">
                    <span class="calculator-icon">🧮</span>
                    <h3>VisiCalc</h3>
                </div>
                <button class="calculator-close-btn" id="calculator-close-btn" title="Back to Smokey AI">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>

            <!-- Calculator Body -->
            <div class="calculator-body">
                <div class="calculator-display">
                    <input type="text" id="calc-result" readonly placeholder="0">
                </div>
                <div class="calculator-buttons">
                    <button class="calc-btn operator" data-value="AC">AC</button>
                    <button class="calc-btn operator" data-value="C">C</button>
                    <button class="calc-btn operator" data-value="%">%</button>
                    <button class="calc-btn operator" data-value="/">/</button>

                    <button class="calc-btn" data-value="7">7</button>
                    <button class="calc-btn" data-value="8">8</button>
                    <button class="calc-btn" data-value="9">9</button>
                    <button class="calc-btn operator" data-value="*">×</button>

                    <button class="calc-btn" data-value="4">4</button>
                    <button class="calc-btn" data-value="5">5</button>
                    <button class="calc-btn" data-value="6">6</button>
                    <button class="calc-btn operator" data-value="-">−</button>

                    <button class="calc-btn" data-value="1">1</button>
                    <button class="calc-btn" data-value="2">2</button>
                    <button class="calc-btn" data-value="3">3</button>
                    <button class="calc-btn operator" data-value="+">+</button>

                    <button class="calc-btn zero" data-value="0">0</button>
                    <button class="calc-btn" data-value=".">.</button>
                    <button class="calc-btn equals" data-value="=">=</button>
                </div>
            </div>

            <!-- Calculator Footer -->
            <div class="calculator-footer">
                <div class="calculator-info">
                    <span>Use keyboard for quick input • ESC to close</span>
                </div>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script src="app.js"></script>

    <!-- AI RESPONSE FUNCTIONS -->
    <script>
        // AI Response function using available APIs
        async function getAIResponse(userMessage) {
            const config = getConfig();

            // Try Groq first (fastest)
            if (config.groq.enabled) {
                try {
                    console.log('🚀 Using Groq API for response');
                    return await callGroqAPI(userMessage, config);
                } catch (error) {
                    console.warn('⚠️ Groq API failed, trying DeepSeek:', error);
                }
            }

            // Try DeepSeek as fallback
            if (config.deepseek.enabled) {
                try {
                    console.log('🔧 Using DeepSeek API for response');
                    return await callDeepSeekAPI(userMessage, config);
                } catch (error) {
                    console.warn('⚠️ DeepSeek API failed:', error);
                }
            }

            // Fallback response
            return `Hello! I received your message: "${userMessage}". I'm currently working on connecting to my AI brain! 🧠✨ The send functionality is working perfectly though! 🎉`;
        }

        // Groq API call
        async function callGroqAPI(message, config) {
            const response = await fetch(config.groq.endpoint, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${config.groq.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: config.groq.defaultModel,
                    messages: [
                        {
                            role: 'system',
                            content: 'You are Smokey AI, a helpful and friendly assistant named after a cat. Be conversational, helpful, and engaging. Keep responses concise but informative.'
                        },
                        {
                            role: 'user',
                            content: message
                        }
                    ],
                    temperature: config.groq.temperature,
                    max_tokens: config.groq.maxTokens
                })
            });

            if (!response.ok) {
                throw new Error(`Groq API error: ${response.status}`);
            }

            const data = await response.json();
            return data.choices[0].message.content;
        }

        // DeepSeek API call
        async function callDeepSeekAPI(message, config) {
            const endpoint = config.deepseek.endpoints[config.deepseek.provider];

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${config.deepseek.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: config.deepseek.defaultModel,
                    messages: [
                        {
                            role: 'system',
                            content: 'You are Smokey AI, a helpful and friendly assistant. Be conversational and helpful.'
                        },
                        {
                            role: 'user',
                            content: message
                        }
                    ],
                    temperature: config.deepseek.temperature,
                    max_tokens: config.deepseek.maxTokens
                })
            });

            if (!response.ok) {
                throw new Error(`DeepSeek API error: ${response.status}`);
            }

            const data = await response.json();
            return data.choices[0].message.content;
        }
    </script>

    <!-- CHAT BUBBLE & SCROLL FUNCTIONS -->
    <script>
        // Enhanced scroll function - ChatGPT-style automatic scrolling
        function scrollToBottom(force = false) {
            const output = document.getElementById('output');
            const chatContainer = document.querySelector('.chat-container');

            if (!output) return;

            // Multiple scroll attempts for reliability (like ChatGPT)
            const performScroll = () => {
                // Scroll the main output container
                output.scrollTop = output.scrollHeight;

                // Also scroll the chat container if it exists
                if (chatContainer) {
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }

                // Scroll the window itself if needed
                const lastMessage = output.lastElementChild;
                if (lastMessage) {
                    lastMessage.scrollIntoView({
                        behavior: 'smooth',
                        block: 'end',
                        inline: 'nearest'
                    });
                }

                console.log('📜 Auto-scrolled to bottom - Height:', output.scrollHeight);
            };

            // Immediate scroll
            performScroll();

            // Delayed scroll for content that might still be loading
            setTimeout(performScroll, 50);
            setTimeout(performScroll, 150);

            // Force scroll if requested (for new messages)
            if (force) {
                setTimeout(performScroll, 300);
                setTimeout(performScroll, 500);
            }
        }

        // Scroll observer - automatically scroll when new content is added
        function setupScrollObserver() {
            const output = document.getElementById('output');
            if (!output) return;

            // Create a MutationObserver to watch for new messages
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // New message added - auto scroll
                        scrollToBottom(true);
                    }
                });
            });

            // Start observing
            observer.observe(output, {
                childList: true,
                subtree: true
            });

            console.log('👁️ Scroll observer activated - will auto-scroll on new messages');
        }

        // Format AI response with proper HTML structure
        function formatAIResponse(text) {
            // Basic formatting for better display
            return text
                .replace(/\n\n/g, '</p><p>')
                .replace(/\n/g, '<br>')
                .replace(/^\s*/, '<p>')
                .replace(/\s*$/, '</p>')
                .replace(/<p><\/p>/g, '');
        }

        // NO WELCOME MESSAGE - Start with clean chat
        function showWelcomeMessage() {
            // DO NOTHING - No pre-sent welcome message
            console.log('✅ Clean chat - no welcome message');
        }

        // Simulate typing effect for AI responses (ChatGPT-style)
        function typeMessage(element, text, speed = 30) {
            return new Promise((resolve) => {
                let i = 0;
                element.innerHTML = '';

                const typeChar = () => {
                    if (i < text.length) {
                        element.innerHTML += text.charAt(i);
                        i++;

                        // Scroll during typing to keep up with content
                        if (i % 10 === 0) { // Scroll every 10 characters
                            scrollToBottom();
                        }

                        setTimeout(typeChar, speed);
                    } else {
                        // Final scroll when typing is complete
                        scrollToBottom(true);
                        resolve();
                    }
                };

                typeChar();
            });
        }

        // SIMPLE: Hide ENTIRE HEADER once user sends first message
        let conversationStarted = false;

        function hideMainTitle() {
            const header = document.querySelector('.header');
            const chatContainer = document.querySelector('.chat-container');
            const mainContainer = document.querySelector('.main-container');

            if (header && !conversationStarted) {
                conversationStarted = true;

                // HIDE THE ENTIRE HEADER
                header.style.display = 'none';

                // EXPAND CHAT TO FULL HEIGHT
                if (mainContainer) {
                    mainContainer.style.height = '100vh';
                }
                if (chatContainer) {
                    chatContainer.style.minHeight = '100vh';
                    chatContainer.style.maxHeight = '100vh';
                }

                console.log('✅ ENTIRE HEADER HIDDEN - user started conversation');
            }
        }
    </script>

    <!-- EMERGENCY FIX FOR SEND MESSAGE -->
    <script>
        // DIRECT FIX - Override any issues with simple working code
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚨 EMERGENCY FIX: Setting up direct send functionality');

            // Wait a bit for everything to load
            setTimeout(() => {
                const input = document.getElementById('input');
                const sendButton = document.getElementById('send-button');
                const output = document.getElementById('output');

                console.log('🔍 Elements found:', {
                    input: !!input,
                    sendButton: !!sendButton,
                    output: !!output
                });

                if (input && sendButton && output) {
                    // DIRECT SEND FUNCTION
                    function directSend() {
                        const message = input.value.trim();
                        console.log('📤 DIRECT SEND:', message);

                        if (!message) return;

                        // Hide main title once user sends first message
                        hideMainTitle();

                        // Clear input
                        input.value = '';

                        // Add user message with proper structure
                        const userDiv = document.createElement('div');
                        userDiv.className = 'message user';
                        userDiv.innerHTML = `
                            <div class="message-content">
                                <div class="message-text">${message}</div>
                                <div class="message-time">${new Date().toLocaleTimeString()}</div>
                            </div>
                        `;
                        output.appendChild(userDiv);

                        // Force scroll after user message (ChatGPT-style)
                        scrollToBottom(true);

                        // Add AI response with real API call
                        setTimeout(async () => {
                            const aiDiv = document.createElement('div');
                            aiDiv.className = 'message ai';
                            aiDiv.innerHTML = `
                                <div class="message-content">
                                    <div class="message-text">🤔 Thinking...</div>
                                    <div class="message-time">${new Date().toLocaleTimeString()}</div>
                                </div>
                            `;
                            output.appendChild(aiDiv);

                            // Scroll to show thinking message (ChatGPT-style)
                            scrollToBottom(true);

                            // Get real AI response
                            try {
                                const response = await getAIResponse(message);
                                const messageTextElement = aiDiv.querySelector('.message-text');
                                messageTextElement.innerHTML = formatAIResponse(response);

                                // Force scroll after response is fully loaded
                                setTimeout(() => scrollToBottom(true), 100);

                                console.log('✅ AI Response received:', response.substring(0, 50) + '...');
                            } catch (error) {
                                console.error('❌ AI Response Error:', error);
                                aiDiv.querySelector('.message-text').textContent = `Hello! I received your message: "${message}". I'm working on connecting to my AI brain! 🧠✨`;

                                // Scroll after error message too
                                setTimeout(() => scrollToBottom(true), 100);
                            }

                            // Final scroll to ensure visibility (ChatGPT-style)
                            setTimeout(() => scrollToBottom(true), 200);
                        }, 500);
                    }

                    // ATTACH EVENTS
                    sendButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('🔘 DIRECT: Send button clicked');
                        directSend();
                    });

                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            console.log('⌨️ DIRECT: Enter pressed');
                            directSend();
                        }
                    });

                    console.log('✅ EMERGENCY FIX: Direct send functionality activated!');

                    // Setup automatic scrolling observer (ChatGPT-style)
                    setupScrollObserver();

                    // Setup new chat functionality
                    const newChatBtn = document.getElementById('new-chat');
                    if (newChatBtn) {
                        newChatBtn.addEventListener('click', function() {
                            // Clear chat
                            output.innerHTML = '';

                            // Reset conversation state and SHOW HEADER AGAIN
                            conversationStarted = false;
                            const header = document.querySelector('.header');
                            const chatContainer = document.querySelector('.chat-container');
                            const mainContainer = document.querySelector('.main-container');

                            if (header) {
                                header.style.display = 'block';
                            }
                            if (mainContainer) {
                                mainContainer.style.height = 'calc(100vh - 80px)';
                            }
                            if (chatContainer) {
                                chatContainer.style.minHeight = 'calc(100vh - 80px)';
                                chatContainer.style.maxHeight = 'calc(100vh - 80px)';
                            }

                            // NO welcome message - clean start
                            console.log('🔄 New chat - clean start, no welcome message');

                            console.log('🔄 New chat started - HEADER RESTORED');
                        });
                    }

                    // Fix profile name
                    const userName = document.getElementById('user-name');
                    if (userName && userName.textContent === 'Loading...') {
                        userName.textContent = 'Demo User';
                        console.log('✅ EMERGENCY FIX: Profile name fixed');
                    }

                    // Fix profile button
                    const profileBtn = document.getElementById('profile-btn');
                    if (profileBtn) {
                        profileBtn.addEventListener('click', function() {
                            alert('Profile: Demo User\nStatus: Active\nSession: Demo Mode');
                        });
                        console.log('✅ EMERGENCY FIX: Profile button fixed');
                    }

                    // Fix tools button
                    const toolsBtn = document.getElementById('tools-btn');
                    const toolsPanel = document.getElementById('tools-panel');
                    if (toolsBtn && toolsPanel) {
                        toolsBtn.addEventListener('click', function() {
                            const isVisible = toolsPanel.style.opacity === '1';
                            toolsPanel.style.opacity = isVisible ? '0' : '1';
                            toolsPanel.style.visibility = isVisible ? 'hidden' : 'visible';
                            console.log('🛠️ EMERGENCY FIX: Tools menu toggled');
                        });
                        console.log('✅ EMERGENCY FIX: Tools button fixed');
                    }

                    // Fix image generator button
                    const imageBtn = document.getElementById('image-generator-btn');
                    const imageModal = document.getElementById('image-modal');
                    if (imageBtn && imageModal) {
                        imageBtn.addEventListener('click', function() {
                            imageModal.classList.add('active');
                            console.log('🎨 EMERGENCY FIX: Image modal opened');
                        });
                        console.log('✅ EMERGENCY FIX: Image generator fixed');
                    }

                    // Hide the static welcome message - start with clean chat
                    const welcomeMessage = document.getElementById('welcome-message');
                    if (welcomeMessage) {
                        welcomeMessage.style.display = 'none';
                    }

                    // NO welcome message in chat - start clean
                    console.log('✅ Clean chat started - no pre-sent messages');

                } else {
                    console.error('❌ EMERGENCY FIX: Required elements not found');
                }
            }, 1000);
        });
    </script>

    <!-- Fix for scroll issue and ensure main elements are visible -->
    <script>
        // Immediate scroll fix
        (function() {
            window.scrollTo(0, 0);
            document.documentElement.scrollTop = 0;
            document.body.scrollTop = 0;
        })();

        // Fix scroll position when DOM loads
        document.addEventListener('DOMContentLoaded', function() {
            // Force scroll to top
            window.scrollTo(0, 0);
            document.documentElement.scrollTop = 0;
            document.body.scrollTop = 0;

            // Ensure all main elements are visible and properly positioned
            const welcomeMessage = document.querySelector('.welcome-message');
            const inputArea = document.querySelector('.input-area');
            const chatContainer = document.querySelector('.chat-container');

            // Make sure elements are visible
            if (welcomeMessage) {
                welcomeMessage.style.display = 'flex';
                welcomeMessage.style.visibility = 'visible';
            }

            if (inputArea) {
                inputArea.style.display = 'block';
                inputArea.style.visibility = 'visible';
            }

            // Reset chat container scroll
            if (chatContainer) {
                chatContainer.scrollTop = 0;
            }

            console.log('✅ Page layout fixed - all main elements should be visible');
        });

        // Additional fix when page fully loads
        window.addEventListener('load', function() {
            setTimeout(() => {
                // Final scroll to top
                window.scrollTo(0, 0);
                document.documentElement.scrollTop = 0;
                document.body.scrollTop = 0;

                // Ensure title is visible by scrolling welcome message into view
                const welcomeMessage = document.querySelector('.welcome-message');
                if (welcomeMessage) {
                    welcomeMessage.scrollIntoView({
                        behavior: 'instant',
                        block: 'start',
                        inline: 'nearest'
                    });
                }

                console.log('✅ Final layout adjustment complete');
            }, 200);
        });

        // Prevent any unwanted scrolling
        window.addEventListener('beforeunload', function() {
            window.scrollTo(0, 0);
        });
    </script>
</body>
</html>
